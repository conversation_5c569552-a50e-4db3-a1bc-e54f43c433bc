/**
 * CythroDash - Transfers Controller
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { userOperations } from '../../database/user';
import { transferOperations, CreateTransferData } from '../../database/transfers';
import {
  TransferStatus,
  TransferType,
  TransferSecurityFlags,
  TransferDeviceInfo,
  TransferHelpers
} from '../../../../database/tables/cythro_dash_transfers';
import { SecurityLogsController } from '../Security/Logs';
import { SecurityLogAction } from '../../../../database/tables/cythro_dash_users_logs';

// Request interfaces
export interface SendTransferRequest {
  sender_user_id: number;
  receiver_user_id: number;
  amount: number;
  note?: string;
  ip_address: string;
  user_agent: string;
  session_id?: string;
  device_info?: Partial<TransferDeviceInfo>;
}

export interface ValidateTransferRequest {
  sender_user_id: number;
  receiver_user_id: number;
  amount: number;
}

export interface GetTransferHistoryRequest {
  user_id: number;
  limit?: number;
  offset?: number;
  status?: TransferStatus;
}

export interface GetTransferStatsRequest {
  user_id: number;
  days?: number;
}

// Response interfaces
export interface TransferResponse {
  success: boolean;
  message?: string;
  data?: any;
}

export interface TransferValidationResponse {
  valid: boolean;
  message?: string;
  errors?: string[];
}

/**
 * Transfers Controller
 * Handles all transfer-related operations including sending, validation, and history
 */
export class TransfersController {
  
  /**
   * Send coins from one user to another
   */
  static async sendTransfer(request: SendTransferRequest): Promise<TransferResponse> {
    try {
      console.log('💸 Processing transfer request:', {
        sender: request.sender_user_id,
        receiver: request.receiver_user_id,
        amount: request.amount,
        ip: request.ip_address
      });

      // Check if transfer system is enabled
      if (!this.isTransferSystemEnabled()) {
        console.log('❌ Transfer system is disabled');
        return {
          success: false,
          message: 'Transfer system is currently disabled'
        };
      }

      // Validate the transfer request
      const validation = await this.validateTransfer({
        sender_user_id: request.sender_user_id,
        receiver_user_id: request.receiver_user_id,
        amount: request.amount
      });

      if (!validation.valid) {
        console.log('❌ Transfer validation failed:', validation.message);
        return {
          success: false,
          message: validation.message || 'Transfer validation failed',
          data: { errors: validation.errors }
        };
      }

      // Get sender and receiver data
      const sender = await userOperations.getUserById(request.sender_user_id);
      const receiver = await userOperations.getUserById(request.receiver_user_id);

      if (!sender || !receiver) {
        console.log('❌ User not found');
        return {
          success: false,
          message: 'One or both users not found'
        };
      }

      // Check sufficient balance
      if (sender.coins < request.amount) {
        console.log('❌ Insufficient balance:', { has: sender.coins, needs: request.amount });
        return {
          success: false,
          message: 'Insufficient balance'
        };
      }

      // Detect suspicious activity
      const securityFlags = await this.detectSuspiciousActivity(request, sender);
      
      if (securityFlags.automated_block) {
        console.log('🚫 Transfer automatically blocked due to suspicious activity');
        return {
          success: false,
          message: 'Transfer blocked for security reasons'
        };
      }

      // Create transfer record
      const transferData: CreateTransferData = {
        sender_user_id: request.sender_user_id,
        receiver_user_id: request.receiver_user_id,
        amount: request.amount,
        type: TransferType.USER_TO_USER,
        status: securityFlags.requires_review ? TransferStatus.PENDING : TransferStatus.COMPLETED,
        metadata: {
          note: request.note,
          reference_id: TransferHelpers.generateReferenceId()
        },
        security: securityFlags,
        device_info: {
          user_agent: request.user_agent,
          ip_address: request.ip_address,
          session_id: request.session_id,
          ...request.device_info
        },
        sender_balance_before: sender.coins,
        sender_balance_after: sender.coins - request.amount,
        receiver_balance_before: receiver.coins,
        receiver_balance_after: receiver.coins + request.amount,
        created_by_ip: request.ip_address,
        version: 1
      };

      console.log('💾 Creating transfer record...');
      const transfer = await transferOperations.createTransfer(transferData);
      console.log('✅ Transfer record created:', { id: transfer.id, status: transfer.status });

      // Execute the transfer if not pending review
      if (transfer.status === TransferStatus.COMPLETED) {
        await this.executeTransfer(transfer.id, sender, receiver, request.amount);
      }

      // Log the transfer
      await SecurityLogsController.createLog({
        user_id: request.sender_user_id,
        action: SecurityLogAction.COIN_TRANSFER,
        description: `Sent ${request.amount} coins to user ${receiver.username}`,
        details: {
          transfer_id: transfer.id,
          receiver_id: request.receiver_user_id,
          amount: request.amount,
          status: transfer.status,
          reference_id: transfer.metadata?.reference_id
        },
        ip_address: request.ip_address
      });

      console.log('📝 Security log created for transfer');

      return {
        success: true,
        message: transfer.status === TransferStatus.COMPLETED 
          ? `Successfully sent ${request.amount} coins to ${receiver.username}`
          : 'Transfer submitted for review',
        data: {
          transfer_id: transfer.id,
          reference_id: transfer.metadata?.reference_id,
          status: transfer.status,
          amount: request.amount,
          receiver: {
            id: receiver.id,
            username: receiver.username
          }
        }
      };

    } catch (error) {
      console.error('💥 Error processing transfer:', error);
      return {
        success: false,
        message: 'An error occurred while processing the transfer'
      };
    }
  }

  /**
   * Execute the actual transfer (update balances)
   */
  private static async executeTransfer(
    transferId: number, 
    sender: any, 
    receiver: any, 
    amount: number
  ): Promise<void> {
    try {
      console.log('⚡ Executing transfer:', { transferId, amount });

      // Update sender balance
      await userOperations.updateUser(sender.id, {
        coins: sender.coins - amount
      });

      // Update receiver balance
      await userOperations.updateUser(receiver.id, {
        coins: receiver.coins + amount
      });

      // Update transfer status
      await transferOperations.updateTransfer(transferId, {
        processed_at: new Date(),
        updated_at: new Date()
      });

      console.log('✅ Transfer executed successfully');
    } catch (error) {
      console.error('💥 Error executing transfer:', error);
      
      // Mark transfer as failed
      await transferOperations.updateTransfer(transferId, {
        status: TransferStatus.FAILED,
        failure_reason: 'Database error during execution',
        updated_at: new Date()
      });
      
      throw error;
    }
  }

  /**
   * Validate a transfer request
   */
  static async validateTransfer(request: ValidateTransferRequest): Promise<TransferValidationResponse> {
    const errors: string[] = [];

    try {
      // Basic validation
      if (!TransferHelpers.isValidAmount(request.amount)) {
        errors.push('Invalid transfer amount');
      }

      if (request.sender_user_id === request.receiver_user_id) {
        errors.push('Cannot transfer to yourself');
      }

      // Check users exist
      const sender = await userOperations.getUserById(request.sender_user_id);
      const receiver = await userOperations.getUserById(request.receiver_user_id);

      if (!sender) {
        errors.push('Sender not found');
      }

      if (!receiver) {
        errors.push('Receiver not found');
      }

      // Check daily limits
      if (sender) {
        const withinLimits = await transferOperations.validateDailyLimits(request.sender_user_id, request.amount);
        if (!withinLimits) {
          errors.push('Daily transfer limit exceeded');
        }

        // Check cooldown
        const cooldownOk = await transferOperations.checkTransferCooldown(request.sender_user_id);
        if (!cooldownOk) {
          errors.push('Transfer cooldown active. Please wait before sending another transfer.');
        }
      }

      return {
        valid: errors.length === 0,
        message: errors.length > 0 ? errors[0] : 'Transfer validation passed',
        errors: errors.length > 0 ? errors : undefined
      };

    } catch (error) {
      console.error('Error validating transfer:', error);
      return {
        valid: false,
        message: 'Validation error occurred',
        errors: ['System error during validation']
      };
    }
  }

  /**
   * Get transfer history for a user
   */
  static async getTransferHistory(request: GetTransferHistoryRequest): Promise<TransferResponse> {
    try {
      console.log('📋 Getting transfer history for user:', request.user_id);

      const transfers = await transferOperations.getTransfersByUser(
        request.user_id,
        request.limit || 50,
        request.offset || 0
      );

      // Get user details for display
      const userIds = new Set<number>();
      transfers.forEach(transfer => {
        userIds.add(transfer.sender_user_id);
        userIds.add(transfer.receiver_user_id);
      });

      const users = await Promise.all(
        Array.from(userIds).map(id => userOperations.getUserById(id))
      );
      const userMap = new Map(users.filter(u => u !== null).map(u => [u!.id, u!]));

      // Format transfers for frontend
      const formattedTransfers = transfers.map(transfer => ({
        id: transfer.id,
        amount: transfer.amount,
        type: transfer.type,
        status: transfer.status,
        note: transfer.metadata?.note,
        reference_id: transfer.metadata?.reference_id,
        created_at: transfer.created_at,
        processed_at: transfer.processed_at,
        is_sender: transfer.sender_user_id === request.user_id,
        other_user: transfer.sender_user_id === request.user_id
          ? {
              id: transfer.receiver_user_id,
              username: userMap.get(transfer.receiver_user_id)?.username || 'Unknown'
            }
          : {
              id: transfer.sender_user_id,
              username: userMap.get(transfer.sender_user_id)?.username || 'Unknown'
            }
      }));

      return {
        success: true,
        data: {
          transfers: formattedTransfers,
          total: transfers.length,
          has_more: transfers.length === (request.limit || 50)
        }
      };

    } catch (error) {
      console.error('Error getting transfer history:', error);
      return {
        success: false,
        message: 'Failed to retrieve transfer history'
      };
    }
  }

  /**
   * Get transfer statistics for a user
   */
  static async getTransferStats(request: GetTransferStatsRequest): Promise<TransferResponse> {
    try {
      console.log('📊 Getting transfer stats for user:', request.user_id);

      const stats = await transferOperations.getUserTransferStats(
        request.user_id,
        request.days || 30
      );

      return {
        success: true,
        data: stats
      };

    } catch (error) {
      console.error('Error getting transfer stats:', error);
      return {
        success: false,
        message: 'Failed to retrieve transfer statistics'
      };
    }
  }

  /**
   * Detect suspicious activity for fraud prevention
   */
  private static async detectSuspiciousActivity(
    request: SendTransferRequest,
    sender: any
  ): Promise<TransferSecurityFlags> {
    try {
      // Get user's transfer history for analysis
      const recentTransfers = await transferOperations.getTransfersByUser(sender.id, 100);

      // Calculate risk score
      const riskScore = TransferHelpers.calculateRiskScore(
        { amount: request.amount, sender_user_id: request.sender_user_id },
        recentTransfers
      );

      const fraudIndicators: string[] = [];
      let requiresReview = false;
      let automatedBlock = false;

      // Large amount check
      if (request.amount > 50000) {
        fraudIndicators.push('Large transfer amount');
        requiresReview = true;
      }

      // Frequency check
      const todayTransfers = recentTransfers.filter(t =>
        t.created_at > new Date(Date.now() - 24 * 60 * 60 * 1000)
      );

      if (todayTransfers.length > 20) {
        fraudIndicators.push('High frequency transfers');
        requiresReview = true;
      }

      if (todayTransfers.length > 50) {
        fraudIndicators.push('Excessive transfer frequency');
        automatedBlock = true;
      }

      // New user check
      if (recentTransfers.length < 3 && request.amount > 10000) {
        fraudIndicators.push('New user large transfer');
        requiresReview = true;
      }

      return {
        is_suspicious: riskScore > 50 || fraudIndicators.length > 0,
        risk_score: riskScore,
        fraud_indicators: fraudIndicators,
        requires_review: requiresReview,
        automated_block: automatedBlock
      };

    } catch (error) {
      console.error('Error detecting suspicious activity:', error);
      // Default to safe settings on error
      return {
        is_suspicious: true,
        risk_score: 75,
        fraud_indicators: ['Error during fraud detection'],
        requires_review: true,
        automated_block: false
      };
    }
  }

  /**
   * Check if transfer system is enabled
   */
  private static isTransferSystemEnabled(): boolean {
    const envValue = process.env.NEXT_PUBLIC_TRANSFER_SYSTEM;
    // Enable by default if not explicitly disabled
    const enabled = envValue !== 'false';
    console.log('🔧 Transfer system enabled:', enabled, '(env value:', envValue, ')');
    return enabled;
  }
}
