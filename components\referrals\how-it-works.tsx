"use client"

import { <PERSON>hare2, <PERSON><PERSON><PERSON><PERSON>, Coins } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"

export default function HowItWorks() {
  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center">
            <Coins className="w-5 h-5 text-white" />
          </div>
          How It Works
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center shadow-lg">
              <Share2 className="w-8 h-8 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-foreground mb-2">1. Share Your Code</h3>
              <p className="text-sm text-muted-foreground">
                Copy your unique referral code and share it with friends through social media, email, or direct messages.
              </p>
            </div>
          </div>
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center shadow-lg">
              <UserPlus className="w-8 h-8 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-foreground mb-2">2. Friend Joins</h3>
              <p className="text-sm text-muted-foreground">Your friend signs up using your referral code</p>
            </div>
          </div>
          <div className="text-center space-y-4">
            <div className="w-16 h-16 mx-auto rounded-2xl bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center shadow-lg">
              <Coins className="w-8 h-8 text-white" />
            </div>
            <div>
              <h3 className="text-lg font-bold text-foreground mb-2">3. Earn Rewards</h3>
              <p className="text-sm text-muted-foreground">Both you and your friend get bonus coins!</p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
