"use client"

import { Check<PERSON>ir<PERSON>, Trophy, Award, Crown, Gem } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { ReferralData } from "@/stores/referral-store"

interface TierStatusProps {
  referralData: ReferralData
}

// Icon mapping function to convert icon names to React components
const getIconComponent = (iconName: string) => {
  const iconMap: Record<string, any> = {
    Trophy,
    Award,
    Crown,
    Gem
  }
  return iconMap[iconName] || Trophy
}

export default function TierStatus({ referralData }: TierStatusProps) {
  // Safely extract tier data with fallbacks
  const currentTier = referralData.stats.current_tier
  const tierProgress = referralData.stats.tier_progress

  // If tier data is not available, return a loading state
  if (!currentTier || !tierProgress) {
    return (
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-lg">
            <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-amber-600 to-orange-700 flex items-center justify-center">
              <CheckCircle className="w-4 h-4 text-white" />
            </div>
            Loading Tier Status...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-gray-200 rounded w-3/4"></div>
            <div className="h-4 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    )
  }

  const { nextTier, progress, referralsToNext } = tierProgress

  // Get the appropriate icon component
  const IconComponent = getIconComponent(currentTier.iconName || 'Trophy')

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-lg">
          <div
            className={`w-6 h-6 rounded-lg bg-gradient-to-br ${currentTier.color} flex items-center justify-center`}
          >
            <IconComponent className="w-4 h-4 text-white" />
          </div>
          Current Tier: {currentTier.name}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="text-center">
          <p className="text-3xl font-black text-foreground mb-1">+{currentTier.bonusPercentage}%</p>
          <p className="text-sm text-muted-foreground">Bonus on all referral rewards</p>
        </div>

        <div className="space-y-2">
          <p className="text-sm font-medium text-foreground">Tier Benefits:</p>
          <ul className="space-y-1">
            {currentTier.benefits.map((benefit, index) => (
              <li key={index} className="flex items-center gap-2 text-sm text-muted-foreground">
                <CheckCircle className="w-3 h-3 text-emerald-400 flex-shrink-0" />
                {benefit}
              </li>
            ))}
          </ul>
        </div>

        {nextTier && (
          <div className="space-y-3 pt-4 border-t border-border/50">
            <div className="flex items-center justify-between">
              <p className="text-sm font-medium text-foreground">Next Tier:</p>
              <Badge className={nextTier.bgColor + " " + nextTier.textColor + " " + nextTier.borderColor}>
                {nextTier.name}
              </Badge>
            </div>
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Progress</span>
                <span className="text-foreground font-medium">
                  {tierProgress.totalReferrals} / {nextTier.minReferrals} referrals
                </span>
              </div>
              <Progress value={progress} className="h-2" />
              <p className="text-xs text-muted-foreground">
                {referralsToNext} more referral{referralsToNext !== 1 ? 's' : ''} needed to reach {nextTier.name} tier
              </p>
            </div>
          </div>
        )}

        {!nextTier && (
          <div className="text-center pt-4 border-t border-border/50">
            <Badge className="bg-gradient-to-r from-purple-500/10 to-pink-500/10 text-purple-400 border-purple-400/20">
              Maximum Tier Reached
            </Badge>
            <p className="text-xs text-muted-foreground mt-2">
              You've achieved the highest tier! Keep referring to maximize your earnings.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
