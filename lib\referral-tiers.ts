import { Trophy, Award, Crown, Gem } from "lucide-react"

// Tier definitions
export interface ReferralTier {
  id: string
  name: string
  minReferrals: number
  bonusPercentage: number
  color: string
  bgColor: string
  textColor: string
  borderColor: string
  icon?: typeof Trophy  // Optional for server-side compatibility
  iconName?: string     // For server-side tier data
  benefits: string[]
  description: string
}

// Define all tiers
export const REFERRAL_TIERS: ReferralTier[] = [
  {
    id: "bronze",
    name: "Bronze",
    minReferrals: 0,
    bonusPercentage: 10,
    color: "from-amber-600 to-orange-700",
    bgColor: "bg-amber-500/10",
    textColor: "text-amber-600",
    borderColor: "border-amber-500/20",
    icon: Trophy,
    benefits: [
      "10% bonus on all referral earnings",
      "Basic referral tracking",
      "Standard support"
    ],
    description: "Start your referral journey with Bronze tier benefits"
  },
  {
    id: "silver",
    name: "Silver",
    minReferrals: 10,
    bonusPercentage: 25,
    color: "from-slate-400 to-slate-600",
    bgColor: "bg-slate-500/10",
    textColor: "text-slate-600",
    borderColor: "border-slate-500/20",
    icon: Award,
    benefits: [
      "25% bonus on all referral earnings",
      "Advanced referral analytics",
      "Priority support",
      "Custom referral codes"
    ],
    description: "Unlock enhanced benefits with Silver tier status"
  },
  {
    id: "gold",
    name: "Gold",
    minReferrals: 25,
    bonusPercentage: 50,
    color: "from-yellow-500 to-amber-600",
    bgColor: "bg-yellow-500/10",
    textColor: "text-yellow-600",
    borderColor: "border-yellow-500/20",
    icon: Crown,
    benefits: [
      "50% bonus on all referral earnings",
      "Premium referral analytics",
      "VIP support access",
      "Custom referral landing pages",
      "Monthly bonus rewards"
    ],
    description: "Experience premium benefits with Gold tier privileges"
  },
  {
    id: "diamond",
    name: "Diamond",
    minReferrals: 50,
    bonusPercentage: 100,
    color: "from-blue-500 to-purple-600",
    bgColor: "bg-blue-500/10",
    textColor: "text-blue-600",
    borderColor: "border-blue-500/20",
    icon: Gem,
    benefits: [
      "100% bonus on all referral earnings",
      "Elite referral analytics dashboard",
      "Dedicated account manager",
      "Custom branding options",
      "Exclusive events and rewards",
      "Early access to new features"
    ],
    description: "Enjoy the ultimate referral experience with Diamond tier"
  }
]

// Tier calculation utilities
export interface TierProgress {
  currentTier: ReferralTier
  nextTier: ReferralTier | null
  progress: number // 0-100 percentage to next tier
  referralsToNext: number
  totalReferrals: number
}

/**
 * Calculate current tier based on successful referrals
 */
export function calculateCurrentTier(successfulReferrals: number): ReferralTier {
  // Find the highest tier the user qualifies for
  let currentTier = REFERRAL_TIERS[0] // Default to Bronze
  
  for (const tier of REFERRAL_TIERS) {
    if (successfulReferrals >= tier.minReferrals) {
      currentTier = tier
    } else {
      break
    }
  }
  
  return currentTier
}

/**
 * Calculate tier progression information
 */
export function calculateTierProgress(successfulReferrals: number): TierProgress {
  const currentTier = calculateCurrentTier(successfulReferrals)
  const currentTierIndex = REFERRAL_TIERS.findIndex(tier => tier.id === currentTier.id)
  const nextTier = currentTierIndex < REFERRAL_TIERS.length - 1 
    ? REFERRAL_TIERS[currentTierIndex + 1] 
    : null
  
  let progress = 100 // Default to 100% if at max tier
  let referralsToNext = 0
  
  if (nextTier) {
    const referralsInCurrentTier = successfulReferrals - currentTier.minReferrals
    const referralsNeededForNext = nextTier.minReferrals - currentTier.minReferrals
    progress = Math.min(100, (referralsInCurrentTier / referralsNeededForNext) * 100)
    referralsToNext = nextTier.minReferrals - successfulReferrals
  }
  
  return {
    currentTier,
    nextTier,
    progress,
    referralsToNext: Math.max(0, referralsToNext),
    totalReferrals: successfulReferrals
  }
}

/**
 * Apply tier bonus to base earnings
 */
export function applyTierBonus(baseEarnings: number, tier: ReferralTier): number {
  const bonus = baseEarnings * (tier.bonusPercentage / 100)
  return Math.round(baseEarnings + bonus)
}

/**
 * Calculate total earnings with tier bonus
 */
export function calculateTierEarnings(baseEarnings: number, successfulReferrals: number): {
  baseEarnings: number
  tierBonus: number
  totalEarnings: number
  tier: ReferralTier
} {
  const tier = calculateCurrentTier(successfulReferrals)
  const tierBonus = Math.round(baseEarnings * (tier.bonusPercentage / 100))
  const totalEarnings = baseEarnings + tierBonus
  
  return {
    baseEarnings,
    tierBonus,
    totalEarnings,
    tier
  }
}

/**
 * Get tier by ID
 */
export function getTierById(tierId: string): ReferralTier | null {
  return REFERRAL_TIERS.find(tier => tier.id === tierId) || null
}

/**
 * Get all tiers for display
 */
export function getAllTiers(): ReferralTier[] {
  return REFERRAL_TIERS
}

/**
 * Check if user can advance to next tier
 */
export function canAdvanceToNextTier(successfulReferrals: number): boolean {
  const currentTier = calculateCurrentTier(successfulReferrals)
  const currentTierIndex = REFERRAL_TIERS.findIndex(tier => tier.id === currentTier.id)
  return currentTierIndex < REFERRAL_TIERS.length - 1
}

/**
 * Get tier color classes for UI
 */
export function getTierColorClasses(tier: ReferralTier): {
  gradient: string
  background: string
  text: string
  border: string
} {
  return {
    gradient: tier.color,
    background: tier.bgColor,
    text: tier.textColor,
    border: tier.borderColor
  }
}
