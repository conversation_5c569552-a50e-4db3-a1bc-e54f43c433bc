"use client"

import { useEffect } from "react"
import { useAuthStore } from "@/stores/user-store"

/**
 * AuthInitializer - Handles initial session validation on app startup
 * This component should be included once in the app layout to handle
 * session restoration from persisted storage
 */
export default function AuthInitializer() {
  const { isAuthenticated, currentUser, sessionToken } = useAuthStore()

  useEffect(() => {
    // Only log the initial state, don't make any API calls
    const logInitialState = () => {
      if (isAuthenticated && currentUser) {
        console.log('Auth store initialized with user:', currentUser.username)
      } else if (sessionToken) {
        console.log('Auth store has session token but no user data')
      } else {
        console.log('Auth store initialized with no session data')
      }
    }

    logInitialState()
  }, []) // Empty dependency array - only run once on mount

  // This component doesn't render anything
  return null
}
