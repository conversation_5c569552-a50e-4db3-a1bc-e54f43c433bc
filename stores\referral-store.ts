"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"
import { Re<PERSON>ralTier, TierProgress, calculateCurrentTier, calculateTierProgress, calculateTierEarnings } from "@/lib/referral-tiers"
import { useAuthStore } from "@/stores/user-store"

// Referral data interfaces
export interface ReferralStats {
  total_clicks: number
  total_signups: number
  total_earnings: number
  pending_earnings: number
  claimed_earnings: number
  conversion_rate: number
  average_earnings_per_referral: number
  last_activity?: string
  // Tier-related stats
  successful_referrals: number
  base_earnings: number
  tier_bonus: number
  current_tier: ReferralTier
  tier_progress: TierProgress
}

export interface ReferralActivity {
  id: number
  action_type: 'click' | 'signup' | 'claim'
  coins_earned: number
  status: 'pending' | 'claimed' | 'expired'
  created_at: string
  is_suspicious: boolean
}

export interface ReferralData {
  stats: ReferralStats
  referral_code: string
  recent_activities: ReferralActivity[]
}

// Referral store state
interface ReferralStore {
  // Data state
  referralData: ReferralData | null
  isLoading: boolean
  error: string | null
  lastFetch: Date | null

  // Actions
  fetchReferralStats: () => Promise<{ success: boolean; message?: string }>
  claimEarnings: () => Promise<{ success: boolean; message?: string; coins_claimed?: number }>
  trackReferralClick: (referralCode: string) => Promise<{ success: boolean; message?: string }>
  trackReferralSignup: (referralCode: string, userId: number) => Promise<{ success: boolean; message?: string }>
  clearError: () => void
  clearData: () => void
}

// Create referral store
export const useReferralStore = create<ReferralStore>()(
  persist(
    (set, get) => ({
      // Initial state
      referralData: null,
      isLoading: false,
      error: null,
      lastFetch: null,

      // Fetch referral statistics
      fetchReferralStats: async () => {
        set({ isLoading: true, error: null })

        try {
          console.log('📊 Fetching referral stats directly from controller...')

          // Get current user from auth store
          const authStore = useAuthStore.getState()
          const currentUser = authStore.currentUser

          if (!currentUser) {
            console.log('❌ No authenticated user found')
            set({
              error: 'User not authenticated',
              isLoading: false
            })
            return { success: false, message: 'User not authenticated' }
          }

          // Call API endpoint
          const response = await fetch('/api/referrals/stats', {
            method: 'GET',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include', // Include cookies for authentication
          })

          const result = await response.json()

          if (result.success && result.data) {
            let enhancedData = result.data

            // Always calculate tier data on the frontend for consistency
            const successfulReferrals = enhancedData.stats.total_signups || 0
            const baseEarnings = enhancedData.stats.total_earnings || 0

            const currentTier = calculateCurrentTier(successfulReferrals)
            const tierProgress = calculateTierProgress(successfulReferrals)
            const tierEarnings = calculateTierEarnings(baseEarnings, successfulReferrals)

            // Create enhanced data with proper typing
            const finalData = {
              ...enhancedData,
              stats: {
                ...enhancedData.stats,
                successful_referrals: successfulReferrals,
                base_earnings: tierEarnings.baseEarnings,
                tier_bonus: tierEarnings.tierBonus,
                current_tier: currentTier,
                tier_progress: tierProgress
              }
            }

            set({
              referralData: finalData as any, // Type assertion to avoid complex type issues
              isLoading: false,
              lastFetch: new Date()
            })
            return { success: true }
          } else {
            set({
              error: result.message || 'Failed to fetch referral stats',
              isLoading: false
            })
            return {
              success: false,
              message: result.message || 'Failed to fetch referral stats'
            }
          }
        } catch (error) {
          console.error('Controller referral stats error:', error)
          const errorMessage = 'Error occurred while fetching referral stats'
          set({
            error: errorMessage,
            isLoading: false
          })
          return {
            success: false,
            message: errorMessage
          }
        }
      },

      // Claim referral earnings
      claimEarnings: async () => {
        const currentData = get().referralData
        if (!currentData || currentData.stats.pending_earnings <= 0) {
          return {
            success: false,
            message: 'No pending earnings to claim'
          }
        }

        set({ isLoading: true, error: null })

        try {
          console.log('💰 Claiming earnings directly from controller...')

          // Get current user from auth store
          const authStore = useAuthStore.getState()
          const currentUser = authStore.currentUser

          if (!currentUser) {
            console.log('❌ No authenticated user found')
            set({
              error: 'User not authenticated',
              isLoading: false
            })
            return { success: false, message: 'User not authenticated' }
          }

          // Call API endpoint
          const response = await fetch('/api/referrals/claim', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include', // Include cookies for authentication
          })

          const result = await response.json()

          if (result.success) {
            const claimedAmount = result.data?.coins_claimed || 0

            // Immediately update user's coin balance in auth store
            if (claimedAmount > 0) {
              console.log('💰 Updating user coins immediately after claim:', claimedAmount)
              authStore.syncCoinsFromClaim(claimedAmount)
            }

            // Refresh referral data after successful claim
            await get().fetchReferralStats()

            return {
              success: true,
              message: result.message || 'Earnings claimed successfully',
              coins_claimed: claimedAmount
            }
          } else {
            set({
              error: result.message || 'Failed to claim earnings',
              isLoading: false
            })
            return {
              success: false,
              message: result.message || 'Failed to claim earnings'
            }
          }
        } catch (error) {
          console.error('Controller claim earnings error:', error)
          const errorMessage = 'Error occurred while claiming earnings'
          set({
            error: errorMessage,
            isLoading: false
          })
          return {
            success: false,
            message: errorMessage
          }
        }
      },

      // Track referral click
      trackReferralClick: async (referralCode: string) => {
        try {
          console.log('🔗 Tracking referral click directly from controller...')

          // Collect device information
          const deviceInfo = {
            screen_resolution: typeof window !== 'undefined' ? `${window.screen.width}x${window.screen.height}` : undefined,
            timezone: typeof window !== 'undefined' ? Intl.DateTimeFormat().resolvedOptions().timeZone : undefined,
            language: typeof window !== 'undefined' ? navigator.language : undefined,
            user_agent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
          }

          // Call API endpoint
          const response = await fetch('/api/referrals/track-click', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify({
              referral_code: referralCode,
              device_info: deviceInfo,
              session_id: `session_${Date.now()}_${Math.random().toString(36).substring(2)}`
            })
          })

          const result = await response.json()

          if (result.success) {
            return {
              success: true,
              message: result.message || 'Referral click tracked successfully'
            }
          } else {
            return {
              success: false,
              message: result.message || 'Failed to track referral click'
            }
          }
        } catch (error) {
          console.error('Controller track referral click error:', error)
          return {
            success: false,
            message: 'Error occurred while tracking referral click'
          }
        }
      },

      // Track referral signup
      trackReferralSignup: async (referralCode: string, userId: number) => {
        try {
          console.log('👤 Tracking referral signup directly from controller...')

          // Collect device information
          const deviceInfo = {
            screen_resolution: typeof window !== 'undefined' ? `${window.screen.width}x${window.screen.height}` : undefined,
            timezone: typeof window !== 'undefined' ? Intl.DateTimeFormat().resolvedOptions().timeZone : undefined,
            language: typeof window !== 'undefined' ? navigator.language : undefined,
            user_agent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
          }

          // Call controller directly
          const result = await ReferralsController.handleReferralSignup({
            referral_code: referralCode,
            referred_user_id: userId,
            device_info: deviceInfo,
            session_id: `session_${Date.now()}_${Math.random().toString(36).substring(2)}`,
            ip_address: 'unknown', // Will be handled by controller
            user_agent: deviceInfo.user_agent || 'unknown'
          })

          if (result.success) {
            return {
              success: true,
              message: result.message || 'Referral signup tracked successfully'
            }
          } else {
            return {
              success: false,
              message: result.message || 'Failed to track referral signup'
            }
          }
        } catch (error) {
          console.error('Controller track referral signup error:', error)
          return {
            success: false,
            message: 'Error occurred while tracking referral signup'
          }
        }
      },

      // Clear error
      clearError: () => {
        set({ error: null })
      },

      // Clear all data
      clearData: () => {
        set({
          referralData: null,
          isLoading: false,
          error: null,
          lastFetch: null
        })
      }
    }),
    {
      name: "referral-store",
      partialize: (state) => ({
        referralData: state.referralData,
        lastFetch: state.lastFetch
      }),
      // Don't persist loading states or errors
      onRehydrateStorage: () => (state) => {
        if (state) {
          // Clear loading and error states on rehydration
          state.isLoading = false
          state.error = null
        }
      }
    }
  )
)
