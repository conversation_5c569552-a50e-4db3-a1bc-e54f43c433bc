"use client"

import { useState } from "react"
import { Send, User, Co<PERSON>, ArrowRight, CheckCircle, AlertTriangle } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { useTransferStore } from "@/stores/transfer-store"
import { useAuthStore } from "@/stores/user-store"

export default function TransferForm() {
  const { toast } = useToast()
  const { currentUser } = useAuthStore()
  const { sendTransfer, validateTransfer } = useTransferStore()
  
  const [recipient, setRecipient] = useState("")
  const [amount, setAmount] = useState("")
  const [note, setNote] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [validation, setValidation] = useState<{ valid: boolean; message?: string } | null>(null)

  const quickAmounts = [10, 25, 50, 100, 500]
  const userBalance = currentUser?.coins || 0

  // Validate transfer in real-time
  const handleValidation = async () => {
    if (!recipient || !amount) {
      setValidation(null)
      return
    }

    const amountNum = parseInt(amount)
    if (isNaN(amountNum) || amountNum <= 0) {
      setValidation({ valid: false, message: "Invalid amount" })
      return
    }

    try {
      const result = await validateTransfer({
        receiver_user_id: parseInt(recipient),
        amount: amountNum,
        note
      })
      setValidation(result)
    } catch (error) {
      setValidation({ valid: false, message: "Validation error" })
    }
  }

  const handleTransfer = async () => {
    if (!recipient || !amount) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive"
      })
      return
    }

    const amountNum = parseInt(amount)
    if (isNaN(amountNum) || amountNum <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid amount",
        variant: "destructive"
      })
      return
    }

    if (amountNum > userBalance) {
      toast({
        title: "Insufficient Balance",
        description: `You only have ${userBalance} coins available`,
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)

    try {
      const result = await sendTransfer({
        receiver_user_id: parseInt(recipient),
        amount: amountNum,
        note: note || undefined
      })

      if (result.success) {
        toast({
          title: "Transfer Successful! 🎉",
          description: result.message || `Successfully sent ${amountNum} coins`,
        })

        // Reset form
        setRecipient("")
        setAmount("")
        setNote("")
        setValidation(null)
      } else {
        toast({
          title: "Transfer Failed",
          description: result.message || "Failed to send transfer",
          variant: "destructive"
        })
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleQuickAmount = (quickAmount: number) => {
    setAmount(quickAmount.toString())
  }

  const handleMaxAmount = () => {
    setAmount(userBalance.toString())
  }

  return (
    <div className="space-y-6">
      {/* Transfer Form */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center">
              <Send className="w-5 h-5 text-white" />
            </div>
            Send Coins
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Recipient */}
          <div className="space-y-2">
            <Label htmlFor="recipient" className="text-base font-semibold">
              Recipient User ID
            </Label>
            <div className="relative">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
              <Input
                id="recipient"
                type="number"
                placeholder="Enter user ID"
                value={recipient}
                onChange={(e) => {
                  setRecipient(e.target.value)
                  handleValidation()
                }}
                className="glass-input pl-10 h-12"
              />
            </div>
            <p className="text-sm text-muted-foreground">
              Enter the user ID of the person you want to send coins to
            </p>
          </div>

          {/* Amount */}
          <div className="space-y-4">
            <Label htmlFor="amount" className="text-base font-semibold">
              Amount
            </Label>
            <div className="relative">
              <Coins className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-muted-foreground" />
              <Input
                id="amount"
                type="number"
                placeholder="Enter amount"
                value={amount}
                onChange={(e) => {
                  setAmount(e.target.value)
                  handleValidation()
                }}
                className="glass-input pl-10 h-12"
                min="1"
                max={userBalance}
              />
            </div>

            {/* Quick Amount Buttons */}
            <div className="space-y-3">
              <p className="text-sm font-medium text-foreground">Quick amounts:</p>
              <div className="flex flex-wrap gap-2">
                {quickAmounts.map((quickAmount) => (
                  <Button
                    key={quickAmount}
                    variant="outline"
                    size="sm"
                    onClick={() => handleQuickAmount(quickAmount)}
                    className="secondary-button bg-transparent"
                    disabled={quickAmount > userBalance}
                  >
                    {quickAmount}
                  </Button>
                ))}
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleMaxAmount}
                  className="secondary-button bg-transparent"
                  disabled={userBalance === 0}
                >
                  Max ({userBalance})
                </Button>
              </div>
            </div>
          </div>

          {/* Note */}
          <div className="space-y-2">
            <Label htmlFor="note" className="text-base font-semibold">
              Note (Optional)
            </Label>
            <Input
              id="note"
              placeholder="Add a message..."
              value={note}
              onChange={(e) => setNote(e.target.value)}
              className="glass-input h-12"
              maxLength={100}
            />
            <p className="text-sm text-muted-foreground">Add a personal message with your transfer</p>
          </div>

          {/* Validation Status */}
          {validation && (
            <div className={`p-3 rounded-xl border ${
              validation.valid 
                ? 'bg-emerald-500/10 border-emerald-500/20 text-emerald-600'
                : 'bg-red-500/10 border-red-500/20 text-red-600'
            }`}>
              <div className="flex items-center gap-2">
                {validation.valid ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  <AlertTriangle className="w-4 h-4" />
                )}
                <span className="text-sm font-medium">
                  {validation.valid ? 'Transfer validated successfully' : validation.message}
                </span>
              </div>
            </div>
          )}

          {/* Transfer Summary */}
          {amount && recipient && (
            <div className="p-4 glass-ultra rounded-xl border border-border/50 bg-gradient-to-br from-background/50 to-background/30">
              <h4 className="font-semibold text-foreground mb-3">Transfer Summary</h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">To User ID:</span>
                  <span className="text-foreground font-medium">{recipient}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Amount:</span>
                  <div className="flex items-center gap-1">
                    <Coins className="w-4 h-4 text-[rgb(20,136,204)]" />
                    <span className="text-foreground font-medium">{amount} coins</span>
                  </div>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Fee:</span>
                  <span className="text-emerald-400 font-medium">FREE</span>
                </div>
                {note && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Note:</span>
                    <span className="text-foreground font-medium">{note}</span>
                  </div>
                )}
                <div className="border-t border-border/50 pt-2 mt-3">
                  <div className="flex justify-between">
                    <span className="text-foreground font-semibold">Remaining Balance:</span>
                    <div className="flex items-center gap-1">
                      <Coins className="w-4 h-4 text-[rgb(20,136,204)]" />
                      <span className="text-foreground font-bold">
                        {userBalance - parseInt(amount || "0")} coins
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Transfer Button */}
          <Button
            onClick={handleTransfer}
            disabled={
              !recipient ||
              !amount ||
              parseInt(amount || "0") > userBalance ||
              parseInt(amount || "0") <= 0 ||
              isLoading ||
              (validation !== null && !validation.valid)
            }
            className="premium-button w-full h-12"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                Sending...
              </>
            ) : (
              <>
                <Send className="w-5 h-5 mr-2" />
                Send {amount} Coins
                <ArrowRight className="w-5 h-5 ml-2" />
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Transfer Tips */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-lg">
            <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center">
              <AlertTriangle className="w-4 h-4 text-white" />
            </div>
            Transfer Tips
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-3 text-sm">
            <div className="flex items-start gap-3">
              <CheckCircle className="w-4 h-4 text-emerald-400 mt-0.5 flex-shrink-0" />
              <p className="text-muted-foreground">Double-check the user ID before sending</p>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="w-4 h-4 text-emerald-400 mt-0.5 flex-shrink-0" />
              <p className="text-muted-foreground">Transfers are instant and cannot be reversed</p>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="w-4 h-4 text-emerald-400 mt-0.5 flex-shrink-0" />
              <p className="text-muted-foreground">No fees for any transfer amount</p>
            </div>
            <div className="flex items-start gap-3">
              <CheckCircle className="w-4 h-4 text-emerald-400 mt-0.5 flex-shrink-0" />
              <p className="text-muted-foreground">Minimum transfer amount is 1 coin</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
