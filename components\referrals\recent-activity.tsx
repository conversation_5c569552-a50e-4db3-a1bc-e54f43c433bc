"use client"

import { TrendingUp, Users, Coins } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ReferralData } from "@/stores/referral-store"

interface RecentActivityProps {
  referralData: ReferralData
}

export default function RecentActivity({ referralData }: RecentActivityProps) {
  const getActivityIcon = (actionType: string) => {
    switch (actionType) {
      case 'click':
        return TrendingUp
      case 'signup':
        return Users
      case 'claim':
        return Coins
      default:
        return TrendingUp
    }
  }

  const getStatusBadgeClasses = (status: string, isSuspicious: boolean) => {
    if (isSuspicious) {
      return "bg-red-500/10 text-red-400 border-red-500/20"
    }
    
    switch (status) {
      case 'pending':
        return "bg-yellow-500/10 text-yellow-400 border-yellow-500/20"
      case 'claimed':
        return "bg-emerald-500/10 text-emerald-400 border-emerald-500/20"
      case 'expired':
        return "bg-gray-500/10 text-gray-400 border-gray-500/20"
      default:
        return "bg-blue-500/10 text-blue-400 border-blue-500/20"
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
            <Users className="w-5 h-5 text-white" />
          </div>
          Recent Activity
          <Badge className="bg-purple-400/10 text-purple-400 border-purple-400/20">
            {referralData.recent_activities?.length || 0}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {(!referralData.recent_activities || referralData.recent_activities.length === 0) ? (
            <p className="text-muted-foreground text-center py-4">No activity yet</p>
          ) : (
            referralData.recent_activities.map((activity) => {
              const IconComponent = getActivityIcon(activity.action_type)
              return (
                <div
                  key={activity.id}
                  className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50"
                >
                  <div className="flex items-center gap-4">
                    <div className="w-10 h-10 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center">
                      <IconComponent className="w-5 h-5 text-white" />
                    </div>
                    <div>
                      <p className="font-semibold text-foreground capitalize">{activity.action_type}</p>
                      <p className="text-sm text-muted-foreground">
                        {formatDate(activity.created_at)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center gap-1 mb-1">
                      <Coins className="w-4 h-4 text-[rgb(20,136,204)]" />
                      <span className="font-bold text-foreground">+{activity.coins_earned}</span>
                    </div>
                    <Badge
                      className={getStatusBadgeClasses(activity.status, activity.is_suspicious)}
                    >
                      {activity.is_suspicious ? 'Suspicious' : activity.status}
                    </Badge>
                  </div>
                </div>
              )
            })
          )}
        </div>
      </CardContent>
    </Card>
  )
}
