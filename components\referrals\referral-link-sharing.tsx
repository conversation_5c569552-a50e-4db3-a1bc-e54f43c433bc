"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON>2, <PERSON>hare2, <PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { ReferralData } from "@/stores/referral-store"

interface ReferralLinkSharingProps {
  referralData: ReferralData
  onClaimEarnings: () => Promise<any>
}

export default function ReferralLinkSharing({
  referralData,
  onClaimEarnings
}: ReferralLinkSharingProps) {
  const { toast } = useToast()
  const [isClaimLoading, setIsClaimLoading] = useState(false)

  const copyReferralCode = () => {
    navigator.clipboard.writeText(referralData.referral_code)
    toast({
      title: "Copied!",
      description: "Referral code copied to clipboard",
    })
  }

  const copyR<PERSON>erral<PERSON>ink = () => {
    const link = `${window.location.origin}/auth/register?ref=${referralData.referral_code}`
    navigator.clipboard.writeText(link)
    toast({
      title: "Copied!",
      description: "Referral link copied to clipboard",
    })
  }

  const shareOnSocial = (platform: string) => {
    const link = `${window.location.origin}/auth/register?ref=${referralData.referral_code}`
    const text = "Join me on CythroDash and get bonus coins! Use my referral code:"
    
    let shareUrl = ""
    switch (platform) {
      case "twitter":
        shareUrl = `https://twitter.com/intent/tweet?text=${encodeURIComponent(text)}&url=${encodeURIComponent(link)}`
        break
      case "discord":
        // Discord doesn't have a direct share URL, so we'll copy to clipboard
        navigator.clipboard.writeText(`${text} ${link}`)
        toast({
          title: "Copied!",
          description: "Message copied to clipboard for Discord",
        })
        return
      case "email":
        shareUrl = `mailto:?subject=${encodeURIComponent("Join CythroDash!")}&body=${encodeURIComponent(`${text} ${link}`)}`
        break
    }
    
    if (shareUrl) {
      window.open(shareUrl, "_blank")
    }
  }

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center">
            <Share2 className="w-5 h-5 text-white" />
          </div>
          Your Referral Link
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="space-y-4">
          <div className="flex gap-3">
            <Input 
              value={referralData.referral_code} 
              readOnly 
              className="glass-input h-12 font-mono text-lg" 
            />
            <Button onClick={copyReferralCode} className="premium-button h-12 px-6">
              <Copy className="w-5 h-5 mr-2" />
              Copy Code
            </Button>
          </div>
          
          <div className="flex gap-3">
            <Input
              value={`${window.location.origin}/auth/register?ref=${referralData.referral_code}`}
              readOnly
              className="glass-input h-12 text-sm"
            />
            <Button
              onClick={copyReferralLink}
              variant="outline"
              className="secondary-button bg-transparent h-12 px-6"
            >
              <Link2 className="w-5 h-5 mr-2" />
              Copy Link
            </Button>
          </div>
        </div>

        {/* Earnings claim section */}
        {(referralData.stats.pending_earnings || 0) > 0 && (
          <div className="p-4 glass-ultra rounded-xl border border-emerald-500/20 bg-emerald-500/5">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium text-foreground">Pending Earnings</p>
                <p className="text-sm text-muted-foreground">
                  You have {referralData.stats.pending_earnings || 0} coins ready to claim
                </p>
              </div>
              <Button
                onClick={async () => {
                  setIsClaimLoading(true)
                  try {
                    await onClaimEarnings()
                  } finally {
                    setIsClaimLoading(false)
                  }
                }}
                disabled={isClaimLoading}
                className="premium-button"
              >
                {isClaimLoading ? 'Claiming...' : `Claim ${referralData.stats.pending_earnings || 0} Coins`}
              </Button>
            </div>
          </div>
        )}

        {/* Social sharing */}
        <div className="space-y-3">
          <p className="text-sm font-medium text-foreground">Share on social media:</p>
          <div className="flex flex-wrap gap-3">
            <Button
              onClick={() => shareOnSocial("twitter")}
              variant="outline"
              className="secondary-button bg-transparent"
            >
              <Share2 className="w-4 h-4 mr-2" />
              Twitter
            </Button>
            <Button
              onClick={() => shareOnSocial("discord")}
              variant="outline"
              className="secondary-button bg-transparent"
            >
              <MessageSquare className="w-4 h-4 mr-2" />
              Discord
            </Button>
            <Button
              onClick={() => shareOnSocial("email")}
              variant="outline"
              className="secondary-button bg-transparent"
            >
              <Mail className="w-4 h-4 mr-2" />
              Email
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
