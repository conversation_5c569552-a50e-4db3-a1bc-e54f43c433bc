"use client"

import { Send, Download, TrendingUp, Activity } from "lucide-react"
import { TransferStats as TransferStatsType } from "@/stores/transfer-store"

interface TransferStatsProps {
  stats: TransferStatsType
}

export default function TransferStats({ stats }: TransferStatsProps) {
  return (
    <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-[rgb(20,136,204)]/10 via-[rgb(32,141,209)]/8 to-[rgb(43,50,178)]/10 border border-[rgb(20,136,204)]/20">
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent" />
      <div className="relative p-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Total Sent */}
          <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
            <div className="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br from-red-500 to-pink-600 flex items-center justify-center">
              <Send className="w-6 h-6 text-white" />
            </div>
            <p className="text-2xl font-black text-foreground">{stats.total_sent.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">Total Sent</p>
          </div>

          {/* Total Received */}
          <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
            <div className="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center">
              <Download className="w-6 h-6 text-white" />
            </div>
            <p className="text-2xl font-black text-foreground">{stats.total_received.toLocaleString()}</p>
            <p className="text-sm text-muted-foreground">Total Received</p>
          </div>

          {/* Transfer Count */}
          <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
            <div className="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
            <p className="text-2xl font-black text-foreground">{stats.transfer_count}</p>
            <p className="text-sm text-muted-foreground">Total Transfers</p>
          </div>

          {/* Daily Activity */}
          <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
            <div className="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br from-purple-500 to-indigo-600 flex items-center justify-center">
              <Activity className="w-6 h-6 text-white" />
            </div>
            <p className="text-2xl font-black text-foreground">{stats.daily_count}</p>
            <p className="text-sm text-muted-foreground">Today's Transfers</p>
          </div>
        </div>

        {/* Daily Breakdown */}
        <div className="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="glass-ultra rounded-2xl p-6 border border-border/50">
            <h3 className="text-lg font-bold text-foreground mb-4">Today's Activity</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Sent Today:</span>
                <span className="font-semibold text-red-500">{stats.daily_sent.toLocaleString()} coins</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Received Today:</span>
                <span className="font-semibold text-emerald-500">{stats.daily_received.toLocaleString()} coins</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Net Change:</span>
                <span className={`font-semibold ${
                  stats.daily_received - stats.daily_sent >= 0 
                    ? 'text-emerald-500' 
                    : 'text-red-500'
                }`}>
                  {stats.daily_received - stats.daily_sent >= 0 ? '+' : ''}
                  {(stats.daily_received - stats.daily_sent).toLocaleString()} coins
                </span>
              </div>
            </div>
          </div>

          <div className="glass-ultra rounded-2xl p-6 border border-border/50">
            <h3 className="text-lg font-bold text-foreground mb-4">All Time</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Total Sent:</span>
                <span className="font-semibold text-red-500">{stats.total_sent.toLocaleString()} coins</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Total Received:</span>
                <span className="font-semibold text-emerald-500">{stats.total_received.toLocaleString()} coins</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-muted-foreground">Net Balance:</span>
                <span className={`font-semibold ${
                  stats.total_received - stats.total_sent >= 0 
                    ? 'text-emerald-500' 
                    : 'text-red-500'
                }`}>
                  {stats.total_received - stats.total_sent >= 0 ? '+' : ''}
                  {(stats.total_received - stats.total_sent).toLocaleString()} coins
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
