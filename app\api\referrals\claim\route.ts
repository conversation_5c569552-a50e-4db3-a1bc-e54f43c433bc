/**
 * CythroDash - Referral Claim Earnings API Route
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { authMiddleware } from '@/lib/auth/middleware';
import { ReferralsController } from '@/hooks/managers/controller/User/Referrals';
import { getClientIP } from '@/lib/security/config';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success) {
      return authResult.response!;
    }

    const user = authResult.user;
    const ip = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Claim referral earnings using the controller
    const claimResponse = await ReferralsController.claimReferralEarnings({
      user_id: user.id,
      ip_address: ip,
      user_agent: userAgent
    });

    if (!claimResponse.success) {
      return NextResponse.json(
        {
          success: false,
          message: claimResponse.message,
          errors: claimResponse.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: claimResponse.message,
      data: claimResponse.data
    });

  } catch (error) {
    console.error('Referral claim API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'An unexpected error occurred while claiming earnings',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}
