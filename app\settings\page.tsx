"use client"

import { useState } from "react"
import PanelLayout from "@/components/panel-layout"
import AuthWrapper from "@/components/auth/auth-wrapper"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { User, Shield, FileText, Palette } from "lucide-react"
import SettingsGeneral from "@/components/settings/settings-general"
import SettingsTheme from "@/components/settings/settings-theme"
import SettingsSecurity from "@/components/settings/settings-security"
import SettingsLogs from "@/components/settings/settings-logs"

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState("general")

  return (
    <AuthWrapper requireAuth={true}>
      <PanelLayout title="Settings" subtitle="Manage your account, security, and preferences">
        <div className="max-w-6xl mx-auto">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 glass-ultra border border-border">
              <TabsTrigger value="general" className="flex items-center gap-2">
                <User className="w-4 h-4" />
                <span className="hidden sm:inline">General</span>
              </TabsTrigger>
              <TabsTrigger value="theme" className="flex items-center gap-2">
                <Palette className="w-4 h-4" />
                <span className="hidden sm:inline">Theme</span>
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="w-4 h-4" />
                <span className="hidden sm:inline">Security</span>
              </TabsTrigger>
              <TabsTrigger value="logs" className="flex items-center gap-2">
                <FileText className="w-4 h-4" />
                <span className="hidden sm:inline">Logs</span>
              </TabsTrigger>
            </TabsList>

            <TabsContent value="general" className="space-y-6">
              <SettingsGeneral />
            </TabsContent>

            <TabsContent value="theme" className="space-y-6">
              <SettingsTheme />
            </TabsContent>

            <TabsContent value="security" className="space-y-6">
              <SettingsSecurity />
            </TabsContent>

            <TabsContent value="logs" className="space-y-6">
              <SettingsLogs />
            </TabsContent>
          </Tabs>
        </div>
      </PanelLayout>
    </AuthWrapper>
  )
}
