/**
 * Server-side tier calculation utilities
 * This file contains only the calculation logic without UI dependencies
 */

// Server-safe tier definitions (without UI components)
export interface ServerReferralTier {
  id: string
  name: string
  minReferrals: number
  bonusPercentage: number
  benefits: string[]
  description: string
}

// Define all tiers (server-safe version)
export const SERVER_REFERRAL_TIERS: ServerReferralTier[] = [
  {
    id: "bronze",
    name: "Bronze",
    minReferrals: 0,
    bonusPercentage: 10,
    benefits: [
      "10% bonus on all referral earnings",
      "Basic referral tracking",
      "Standard support"
    ],
    description: "Start your referral journey with Bronze tier benefits"
  },
  {
    id: "silver",
    name: "Silver",
    minReferrals: 10,
    bonusPercentage: 25,
    benefits: [
      "25% bonus on all referral earnings",
      "Advanced referral analytics",
      "Priority support",
      "Custom referral codes"
    ],
    description: "Unlock enhanced benefits with Silver tier status"
  },
  {
    id: "gold",
    name: "Gold",
    minReferrals: 25,
    bonusPercentage: 50,
    benefits: [
      "50% bonus on all referral earnings",
      "Premium referral analytics",
      "VIP support access",
      "Custom referral landing pages",
      "Monthly bonus rewards"
    ],
    description: "Experience premium benefits with Gold tier privileges"
  },
  {
    id: "diamond",
    name: "<PERSON>",
    minReferrals: 50,
    bonusPercentage: 100,
    benefits: [
      "100% bonus on all referral earnings",
      "Elite referral analytics dashboard",
      "Dedicated account manager",
      "Custom branding options",
      "Exclusive events and rewards",
      "Early access to new features"
    ],
    description: "Enjoy the ultimate referral experience with Diamond tier"
  }
]

// Tier calculation utilities (server-safe)
export interface ServerTierProgress {
  currentTier: ServerReferralTier
  nextTier: ServerReferralTier | null
  progress: number // 0-100 percentage to next tier
  referralsToNext: number
  totalReferrals: number
}

/**
 * Calculate current tier based on successful referrals (server-safe)
 */
export function calculateCurrentTierServer(successfulReferrals: number): ServerReferralTier {
  // Find the highest tier the user qualifies for
  let currentTier = SERVER_REFERRAL_TIERS[0] // Default to Bronze
  
  for (const tier of SERVER_REFERRAL_TIERS) {
    if (successfulReferrals >= tier.minReferrals) {
      currentTier = tier
    } else {
      break
    }
  }
  
  return currentTier
}

/**
 * Calculate tier progression information (server-safe)
 */
export function calculateTierProgressServer(successfulReferrals: number): ServerTierProgress {
  const currentTier = calculateCurrentTierServer(successfulReferrals)
  const currentTierIndex = SERVER_REFERRAL_TIERS.findIndex(tier => tier.id === currentTier.id)
  const nextTier = currentTierIndex < SERVER_REFERRAL_TIERS.length - 1 
    ? SERVER_REFERRAL_TIERS[currentTierIndex + 1] 
    : null
  
  let progress = 100 // Default to 100% if at max tier
  let referralsToNext = 0
  
  if (nextTier) {
    const referralsInCurrentTier = successfulReferrals - currentTier.minReferrals
    const referralsNeededForNext = nextTier.minReferrals - currentTier.minReferrals
    progress = Math.min(100, (referralsInCurrentTier / referralsNeededForNext) * 100)
    referralsToNext = nextTier.minReferrals - successfulReferrals
  }
  
  return {
    currentTier,
    nextTier,
    progress,
    referralsToNext: Math.max(0, referralsToNext),
    totalReferrals: successfulReferrals
  }
}

/**
 * Apply tier bonus to base earnings (server-safe)
 */
export function applyTierBonusServer(baseEarnings: number, tier: ServerReferralTier): number {
  const bonus = baseEarnings * (tier.bonusPercentage / 100)
  return Math.round(baseEarnings + bonus)
}

/**
 * Calculate total earnings with tier bonus (server-safe)
 */
export function calculateTierEarningsServer(baseEarnings: number, successfulReferrals: number): {
  baseEarnings: number
  tierBonus: number
  totalEarnings: number
  tier: ServerReferralTier
} {
  const tier = calculateCurrentTierServer(successfulReferrals)
  const tierBonus = Math.round(baseEarnings * (tier.bonusPercentage / 100))
  const totalEarnings = baseEarnings + tierBonus
  
  return {
    baseEarnings,
    tierBonus,
    totalEarnings,
    tier
  }
}

/**
 * Get tier by ID (server-safe)
 */
export function getTierByIdServer(tierId: string): ServerReferralTier | null {
  return SERVER_REFERRAL_TIERS.find(tier => tier.id === tierId) || null
}

/**
 * Get all tiers for display (server-safe)
 */
export function getAllTiersServer(): ServerReferralTier[] {
  return SERVER_REFERRAL_TIERS
}

/**
 * Check if user can advance to next tier (server-safe)
 */
export function canAdvanceToNextTierServer(successfulReferrals: number): boolean {
  const currentTier = calculateCurrentTierServer(successfulReferrals)
  const currentTierIndex = SERVER_REFERRAL_TIERS.findIndex(tier => tier.id === currentTier.id)
  return currentTierIndex < SERVER_REFERRAL_TIERS.length - 1
}

/**
 * Convert server tier to client tier format
 * This function helps bridge server and client tier data
 */
export function convertServerTierToClientFormat(serverTier: ServerReferralTier) {
  // Map server tier to client tier format with UI properties
  const tierUIMap: Record<string, any> = {
    bronze: {
      color: "from-amber-600 to-orange-700",
      bgColor: "bg-amber-500/10",
      textColor: "text-amber-600",
      borderColor: "border-amber-500/20",
      iconName: "Trophy"
    },
    silver: {
      color: "from-slate-400 to-slate-600",
      bgColor: "bg-slate-500/10",
      textColor: "text-slate-600",
      borderColor: "border-slate-500/20",
      iconName: "Award"
    },
    gold: {
      color: "from-yellow-500 to-amber-600",
      bgColor: "bg-yellow-500/10",
      textColor: "text-yellow-600",
      borderColor: "border-yellow-500/20",
      iconName: "Crown"
    },
    diamond: {
      color: "from-blue-500 to-purple-600",
      bgColor: "bg-blue-500/10",
      textColor: "text-blue-600",
      borderColor: "border-blue-500/20",
      iconName: "Gem"
    }
  }

  const uiProps = tierUIMap[serverTier.id] || tierUIMap.bronze

  return {
    ...serverTier,
    ...uiProps
  }
}
