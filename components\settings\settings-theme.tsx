"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Pa<PERSON>, <PERSON>, <PERSON>, <PERSON>, Sparkles } from 'lucide-react'
import { useAppSettings, THEMES, ThemeName } from "@/stores/settings"
import { useToast } from "@/hooks/use-toast"

export default function SettingsTheme() {
  const { currentTheme, setTheme } = useAppSettings()
  const { toast } = useToast()

  const handleThemeChange = async (theme: ThemeName) => {
    try {
      setTheme(theme)
      toast({
        title: "Theme Updated",
        description: `Successfully switched to ${THEMES[theme].displayName} theme.`,
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update theme. Please try again.",
        variant: "destructive"
      })
    }
  }

  const getThemeIcon = (themeName: ThemeName) => {
    switch (themeName) {
      case "light":
        return <Sun className="w-4 h-4" />
      case "dark":
        return <Moon className="w-4 h-4" />
      case "cyberpunk":
        return <Sparkles className="w-4 h-4" />
      default:
        return <Palette className="w-4 h-4" />
    }
  }

  const getThemePreview = (theme: ThemeName) => {
    const config = THEMES[theme]
    return (
      <div 
        className="w-full h-16 rounded-lg border border-border/50 p-3 flex items-center justify-between"
        style={{
          background: `linear-gradient(135deg, ${config.colors.background}, ${config.colors.secondary})`,
          borderColor: config.colors.border
        }}
      >
        <div className="flex items-center gap-2">
          <div 
            className="w-3 h-3 rounded-full"
            style={{ background: config.colors.brandFrom }}
          />
          <div 
            className="w-3 h-3 rounded-full"
            style={{ background: config.colors.brandTo }}
          />
          <div 
            className="w-3 h-3 rounded-full"
            style={{ background: config.colors.accent }}
          />
        </div>
        <div 
          className="text-xs font-medium px-2 py-1 rounded"
          style={{ 
            color: config.colors.foreground,
            background: config.colors.primary + '20'
          }}
        >
          Preview
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Current Theme */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="text-foreground text-xl flex items-center gap-2">
            <Palette className="w-5 h-5" />
            Current Theme
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4 p-4 rounded-xl border border-border/50 glass-ultra">
            {getThemeIcon(currentTheme)}
            <div className="flex-1">
              <h3 className="font-semibold text-foreground">
                {THEMES[currentTheme].displayName}
              </h3>
              <p className="text-sm text-muted-foreground">
                {THEMES[currentTheme].description}
              </p>
            </div>
            <Badge variant="secondary" className="bg-brand/20 text-brand">
              Active
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Theme Selection */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="text-foreground text-xl">Available Themes</CardTitle>
          <p className="text-muted-foreground">
            Choose a theme that matches your style. Changes are applied instantly.
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {Object.entries(THEMES).map(([key, theme]) => {
              const isActive = currentTheme === key
              return (
                <div
                  key={key}
                  className={`relative p-4 rounded-xl border transition-all cursor-pointer hover:scale-105 ${
                    isActive 
                      ? 'border-brand bg-brand/10' 
                      : 'border-border/50 glass-ultra hover:border-brand/50'
                  }`}
                  onClick={() => handleThemeChange(key as ThemeName)}
                >
                  {isActive && (
                    <div className="absolute top-2 right-2">
                      <div className="w-6 h-6 rounded-full bg-brand flex items-center justify-center">
                        <Check className="w-3 h-3 text-white" />
                      </div>
                    </div>
                  )}
                  
                  <div className="space-y-3">
                    <div className="flex items-center gap-2">
                      {getThemeIcon(key as ThemeName)}
                      <h3 className="font-semibold text-foreground">
                        {theme.displayName}
                      </h3>
                    </div>
                    
                    <p className="text-sm text-muted-foreground">
                      {theme.description}
                    </p>
                    
                    {getThemePreview(key as ThemeName)}
                    
                    <Button
                      variant={isActive ? "default" : "outline"}
                      size="sm"
                      className={`w-full ${
                        isActive 
                          ? 'bg-brand hover:bg-brand/90' 
                          : 'secondary-button'
                      }`}
                      onClick={(e) => {
                        e.stopPropagation()
                        handleThemeChange(key as ThemeName)
                      }}
                    >
                      {isActive ? 'Active' : 'Apply Theme'}
                    </Button>
                  </div>
                </div>
              )
            })}
          </div>
        </CardContent>
      </Card>

      {/* Theme Information */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="text-foreground text-xl">Theme Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="p-4 rounded-xl border border-border/50 glass-ultra">
            <h4 className="font-semibold text-foreground mb-2">Automatic Sync</h4>
            <p className="text-sm text-muted-foreground">
              Your theme preference is automatically saved to your account and will be applied 
              across all your devices when you sign in.
            </p>
          </div>
          
          <div className="p-4 rounded-xl border border-border/50 glass-ultra">
            <h4 className="font-semibold text-foreground mb-2">Custom Themes</h4>
            <p className="text-sm text-muted-foreground">
              Want to create your own theme? Custom theme creation will be available in a future update. 
              Stay tuned for more personalization options!
            </p>
          </div>
          
          <div className="p-4 rounded-xl border border-border/50 glass-ultra">
            <h4 className="font-semibold text-foreground mb-2">Performance</h4>
            <p className="text-sm text-muted-foreground">
              All themes are optimized for performance and accessibility. They work seamlessly 
              across different screen sizes and devices.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
