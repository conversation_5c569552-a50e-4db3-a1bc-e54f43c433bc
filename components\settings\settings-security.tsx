"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Switch } from "@/components/ui/switch"
import { Badge } from "@/components/ui/badge"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { 
  Shield, 
  Key, 
  Smartphone, 
  Mail, 
  AlertTriangle, 
  CheckCircle, 
  QrCode,
  Eye,
  EyeOff,
  Lock
} from 'lucide-react'
import { useAuthStore } from "@/stores/user-store"
import { useToast } from "@/hooks/use-toast"

export default function SettingsSecurity() {
  const { currentUser, changePassword } = useAuthStore()
  const { toast } = useToast()
  
  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
  const [emailNotifications, setEmailNotifications] = useState(true)
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: ""
  })
  
  const [isLoading, setIsLoading] = useState(false)

  const handlePasswordChange = async () => {
    if (passwordForm.newPassword !== passwordForm.confirmPassword) {
      toast({
        title: "Error",
        description: "New passwords don't match.",
        variant: "destructive"
      })
      return
    }

    if (passwordForm.newPassword.length < 8) {
      toast({
        title: "Error",
        description: "Password must be at least 8 characters long.",
        variant: "destructive"
      })
      return
    }

    setIsLoading(true)
    try {
      console.log('Starting password change...')

      // Call the password change API
      const result = await changePassword({
        current_password: passwordForm.currentPassword,
        new_password: passwordForm.newPassword,
        confirm_password: passwordForm.confirmPassword
      })

      console.log('Password change result:', result)

      if (result.success) {
        toast({
          title: "Password Updated",
          description: result.message || "Your password has been changed successfully.",
        })
        setPasswordForm({ currentPassword: "", newPassword: "", confirmPassword: "" })
      } else {
        // Show specific error message
        const errorMessage = result.errors && result.errors.length > 0
          ? result.errors.map(err => `${err.field}: ${err.message}`).join(', ')
          : result.message || "Failed to update password. Please try again."

        toast({
          title: "Password Change Failed",
          description: errorMessage,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Password change error:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleTwoFactorToggle = async (enabled: boolean) => {
    setIsLoading(true)
    try {
      // TODO: Implement 2FA toggle API call
      setTwoFactorEnabled(enabled)
      toast({
        title: enabled ? "2FA Enabled" : "2FA Disabled",
        description: enabled 
          ? "Two-factor authentication has been enabled for your account."
          : "Two-factor authentication has been disabled for your account.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to update two-factor authentication settings.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Account Security Status */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="text-foreground text-xl flex items-center gap-2">
            <Shield className="w-5 h-5" />
            Security Status
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 rounded-xl border border-border/50 glass-ultra">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-foreground">Email Verification</span>
                {currentUser?.verified ? (
                  <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Verified
                  </Badge>
                ) : (
                  <Badge variant="secondary" className="bg-yellow-500/20 text-yellow-400">
                    <AlertTriangle className="w-3 h-3 mr-1" />
                    Unverified
                  </Badge>
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                {currentUser?.verified 
                  ? "Your email address has been verified."
                  : "Please verify your email address to secure your account."
                }
              </p>
            </div>

            <div className="p-4 rounded-xl border border-border/50 glass-ultra">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-foreground">Two-Factor Auth</span>
                {twoFactorEnabled ? (
                  <Badge variant="secondary" className="bg-green-500/20 text-green-400">
                    <CheckCircle className="w-3 h-3 mr-1" />
                    Enabled
                  </Badge>
                ) : (
                  <Badge variant="secondary" className="bg-red-500/20 text-red-400">
                    <AlertTriangle className="w-3 h-3 mr-1" />
                    Disabled
                  </Badge>
                )}
              </div>
              <p className="text-xs text-muted-foreground">
                {twoFactorEnabled 
                  ? "Your account is protected with 2FA."
                  : "Enable 2FA for additional security."
                }
              </p>
            </div>
          </div>

          {!currentUser?.verified && (
            <Alert>
              <Mail className="h-4 w-4" />
              <AlertDescription>
                Your email is not verified. 
                <Button variant="link" className="p-0 h-auto ml-1 text-brand">
                  Send verification email
                </Button>
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Change Password */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="text-foreground text-xl flex items-center gap-2">
            <Key className="w-5 h-5" />
            Change Password
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label className="text-foreground">Current Password</Label>
            <div className="relative">
              <Input
                type={showCurrentPassword ? "text" : "password"}
                value={passwordForm.currentPassword}
                onChange={(e) => setPasswordForm(prev => ({ ...prev, currentPassword: e.target.value }))}
                className="glass-ultra border-border pr-10"
                placeholder="Enter your current password"
              />
              <Button
                type="button"
                variant="ghost"
                size="sm"
                className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                onClick={() => setShowCurrentPassword(!showCurrentPassword)}
              >
                {showCurrentPassword ? (
                  <EyeOff className="h-4 w-4 text-muted-foreground" />
                ) : (
                  <Eye className="h-4 w-4 text-muted-foreground" />
                )}
              </Button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label className="text-foreground">New Password</Label>
              <div className="relative">
                <Input
                  type={showNewPassword ? "text" : "password"}
                  value={passwordForm.newPassword}
                  onChange={(e) => setPasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                  className="glass-ultra border-border pr-10"
                  placeholder="Enter new password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowNewPassword(!showNewPassword)}
                >
                  {showNewPassword ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                </Button>
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-foreground">Confirm New Password</Label>
              <div className="relative">
                <Input
                  type={showConfirmPassword ? "text" : "password"}
                  value={passwordForm.confirmPassword}
                  onChange={(e) => setPasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  className="glass-ultra border-border pr-10"
                  placeholder="Confirm new password"
                />
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4 text-muted-foreground" />
                  ) : (
                    <Eye className="h-4 w-4 text-muted-foreground" />
                  )}
                </Button>
              </div>
            </div>
          </div>

          <Button 
            onClick={handlePasswordChange}
            disabled={isLoading || !passwordForm.currentPassword || !passwordForm.newPassword || !passwordForm.confirmPassword}
            className="glass-button bg-gradient-to-r from-[rgb(20,136,204)] to-[rgb(43,50,178)] hover:from-[rgb(20,136,204)]/90 hover:to-[rgb(43,50,178)]/90"
          >
            <Lock className="w-4 h-4 mr-2" />
            {isLoading ? "Updating..." : "Update Password"}
          </Button>
        </CardContent>
      </Card>

      {/* Two-Factor Authentication */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="text-foreground text-xl flex items-center gap-2">
            <Smartphone className="w-5 h-5" />
            Two-Factor Authentication
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 rounded-xl border border-border/50 glass-ultra">
            <div className="space-y-1">
              <h4 className="font-semibold text-foreground">Enable 2FA</h4>
              <p className="text-sm text-muted-foreground">
                Add an extra layer of security to your account
              </p>
            </div>
            <Switch
              checked={twoFactorEnabled}
              onCheckedChange={handleTwoFactorToggle}
              disabled={isLoading}
            />
          </div>

          {twoFactorEnabled && (
            <div className="space-y-4 p-4 rounded-xl border border-border/50 glass-ultra">
              <div className="flex items-center gap-4">
                <div className="w-32 h-32 rounded-lg border border-border/50 flex items-center justify-center bg-muted/20">
                  <QrCode className="w-16 h-16 text-muted-foreground" />
                </div>
                <div className="flex-1 space-y-2">
                  <h4 className="font-semibold text-foreground">Setup Instructions</h4>
                  <ol className="text-sm text-muted-foreground space-y-1">
                    <li>1. Install an authenticator app (Google Authenticator, Authy, etc.)</li>
                    <li>2. Scan the QR code with your authenticator app</li>
                    <li>3. Enter the 6-digit code from your app below</li>
                  </ol>
                </div>
              </div>
              
              <div className="space-y-2">
                <Label className="text-foreground">Verification Code</Label>
                <div className="flex gap-2">
                  <Input
                    placeholder="Enter 6-digit code"
                    className="glass-ultra border-border"
                    maxLength={6}
                  />
                  <Button className="secondary-button">
                    Verify
                  </Button>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Security Notifications */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="text-foreground text-xl flex items-center gap-2">
            <Mail className="w-5 h-5" />
            Security Notifications
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between p-4 rounded-xl border border-border/50 glass-ultra">
            <div className="space-y-1">
              <h4 className="font-semibold text-foreground">Email Alerts</h4>
              <p className="text-sm text-muted-foreground">
                Get notified about important security events
              </p>
            </div>
            <Switch
              checked={emailNotifications}
              onCheckedChange={setEmailNotifications}
            />
          </div>

          <div className="text-sm text-muted-foreground space-y-1">
            <p>You'll receive email notifications for:</p>
            <ul className="list-disc list-inside ml-4 space-y-1">
              <li>New device sign-ins</li>
              <li>Password changes</li>
              <li>Two-factor authentication changes</li>
              <li>Suspicious account activity</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
