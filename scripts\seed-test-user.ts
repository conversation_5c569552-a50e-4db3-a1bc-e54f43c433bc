/**
 * CythroDash - Test User Seeding Script
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { userOperations } from '../hooks/managers/database/user';
import { UserRole, UserTheme, UserLanguage } from '../database/tables/cythro_dash_users';

/**
 * Create a test user for development purposes
 */
export async function seedTestUser() {
  try {
    console.log('Creating test user...');

    // Check if user with ID 1 already exists
    const existingUser = await userOperations.getUserById(1);
    if (existingUser) {
      console.log('Test user already exists:', existingUser.username);
      return existingUser;
    }

    // Create test user
    const testUser = await userOperations.createUser({
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      first_name: 'Test',
      last_name: 'User',
      display_name: 'Test User',
      password_hash: 'dummy_hash', // Not used for authentication
      role: UserRole.USER,
      verified: true,
      coins: 100,
      theme: UserTheme.DARK,
      language: UserLanguage.EN,
      avatar_url: null,
      bio: 'Test user for development',
      website: 'https://example.com',
      timezone: 'UTC',
      notifications_enabled: true,
      email_notifications: true,
      social_links: {
        twitter: '@testuser',
        discord: 'testuser#1234',
        github: 'testuser'
      },
      pterodactyl_uuid: null,
      pterodactyl_id: null,
      banned: false,
      deleted: false,
      created_at: new Date(),
      updated_at: new Date()
    });

    console.log('Test user created successfully:', testUser.username);
    return testUser;

  } catch (error) {
    console.error('Error creating test user:', error);
    throw error;
  }
}

/**
 * Run the seeding script
 */
if (require.main === module) {
  seedTestUser()
    .then(() => {
      console.log('Test user seeding completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('Test user seeding failed:', error);
      process.exit(1);
    });
}
