"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Checkbox } from "@/components/ui/checkbox"
import { Eye, EyeOff, LogIn, AlertCircle, Loader2 } from "lucide-react"
import { useAuthStore } from "@/stores/user-store"
import { useToast } from "@/hooks/use-toast"

export default function LoginPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { login, isAuthenticated, isLoading } = useAuthStore()
  
  const [formData, setFormData] = useState({
    identifier: "",
    password: "",
    remember_me: false
  })
  const [showPassword, setShowPassword] = useState(false)
  const [errors, setErrors] = useState<{ field: string; message: string }[]>([])
  const [generalError, setGeneralError] = useState("")

  // Redirect if already authenticated
  useEffect(() => {
    if (isAuthenticated) {
      router.push("/")
    }
  }, [isAuthenticated, router])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value
    }))
    
    // Clear errors when user starts typing
    if (errors.length > 0) {
      setErrors([])
      setGeneralError("")
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setErrors([])
    setGeneralError("")

    // Basic validation
    if (!formData.identifier.trim()) {
      setErrors([{ field: "identifier", message: "Email or username is required" }])
      return
    }
    if (!formData.password) {
      setErrors([{ field: "password", message: "Password is required" }])
      return
    }

    try {
      const result = await login(formData)
      
      if (result.success) {
        toast({
          title: "Login Successful",
          description: "Welcome back to CythroDash!",
        })
        router.push("/")
      } else {
        if (result.errors && result.errors.length > 0) {
          setErrors(result.errors)
        } else {
          setGeneralError(result.message || "Login failed")
        }
      }
    } catch (error) {
      console.error("Login error:", error)
      setGeneralError("An unexpected error occurred. Please try again.")
    }
  }

  const getFieldError = (field: string) => {
    return errors.find(error => error.field === field)?.message
  }

  if (isAuthenticated) {
    return null // Will redirect
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background/95 to-background/90 p-4">
      <div className="w-full max-w-md">
        <Card className="glass-ultra border border-border shadow-glass">
          <CardHeader className="space-y-1 text-center">
            <CardTitle className="text-2xl font-bold bg-gradient-to-r from-[rgb(20,136,204)] to-[rgb(43,50,178)] bg-clip-text text-transparent">
              Welcome Back
            </CardTitle>
            <CardDescription>
              Sign in to your CythroDash account
            </CardDescription>
          </CardHeader>
          
          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              {generalError && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{generalError}</AlertDescription>
                </Alert>
              )}

              <div className="space-y-2">
                <Label htmlFor="identifier">Email or Username</Label>
                <Input
                  id="identifier"
                  name="identifier"
                  type="text"
                  placeholder="Enter your email or username"
                  value={formData.identifier}
                  onChange={handleInputChange}
                  className={getFieldError("identifier") ? "border-red-500" : ""}
                  disabled={isLoading}
                />
                {getFieldError("identifier") && (
                  <p className="text-sm text-red-500">{getFieldError("identifier")}</p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? "text" : "password"}
                    placeholder="Enter your password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className={getFieldError("password") ? "border-red-500 pr-10" : "pr-10"}
                    disabled={isLoading}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    disabled={isLoading}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-muted-foreground" />
                    ) : (
                      <Eye className="h-4 w-4 text-muted-foreground" />
                    )}
                  </Button>
                </div>
                {getFieldError("password") && (
                  <p className="text-sm text-red-500">{getFieldError("password")}</p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="remember_me"
                    name="remember_me"
                    checked={formData.remember_me}
                    onCheckedChange={(checked) => 
                      setFormData(prev => ({ ...prev, remember_me: checked as boolean }))
                    }
                    disabled={isLoading}
                  />
                  <Label htmlFor="remember_me" className="text-sm">
                    Remember me
                  </Label>
                </div>
                <Link 
                  href="/auth/forgot-password" 
                  className="text-sm text-brand hover:text-brand/80 transition-colors"
                >
                  Forgot password?
                </Link>
              </div>
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <Button 
                type="submit" 
                className="w-full glass-button bg-gradient-to-r from-[rgb(20,136,204)] to-[rgb(43,50,178)] hover:from-[rgb(20,136,204)]/90 hover:to-[rgb(43,50,178)]/90"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  <>
                    <LogIn className="mr-2 h-4 w-4" />
                    Sign In
                  </>
                )}
              </Button>
              
              <p className="text-center text-sm text-muted-foreground">
                Don't have an account?{" "}
                <Link 
                  href="/auth/register" 
                  className="text-brand hover:text-brand/80 transition-colors font-medium"
                >
                  Sign up
                </Link>
              </p>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  )
}
