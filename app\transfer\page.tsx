"use client"

import { useEffect } from "react"
import PanelLayout from "@/components/panel-layout"
import { useToast } from "@/hooks/use-toast"
import { useTransferStore } from "@/stores/transfer-store"

// Import transfer components
import TransferForm from "@/components/transfer/transfer-form"
import TransferStats from "@/components/transfer/transfer-stats"
import TransferHistory from "@/components/transfer/transfer-history"

export default function TransferPage() {
  const { toast } = useToast()
  const {
    transferData,
    error,
    fetchTransferStats,
    fetchTransferHistory,
    clearError
  } = useTransferStore()

  // Fetch transfer data on mount
  useEffect(() => {
    fetchTransferStats()
    fetchTransferHistory()
  }, [fetchTransferStats, fetchTransferHistory])

  // Show error toast when error changes
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: error,
        variant: "destructive"
      })
      clearError()
    }
  }, [error, toast, clearError])

  return (
    <PanelLayout title="Transfer Coins" subtitle="Send coins to other users instantly">
      <div className="space-y-8">
        {/* Transfer Statistics */}
        {transferData?.stats && (
          <TransferStats stats={transferData.stats} />
        )}

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Transfer Form */}
          <div className="lg:col-span-2">
            <TransferForm />
          </div>

          {/* Right Column - Quick Stats */}
          <div className="space-y-6">
            {transferData?.stats && (
              <div className="glass-ultra rounded-2xl p-6 border border-border/50">
                <h3 className="text-lg font-bold text-foreground mb-4">Quick Stats</h3>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Today Sent:</span>
                    <span className="font-semibold text-foreground">{(transferData.stats.daily_sent ?? 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Today Received:</span>
                    <span className="font-semibold text-foreground">{(transferData.stats.daily_received ?? 0).toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Today Count:</span>
                    <span className="font-semibold text-foreground">{transferData.stats.daily_count ?? 0}</span>
                  </div>
                  <div className="border-t border-border/50 pt-3">
                    <div className="flex justify-between">
                      <span className="text-muted-foreground">Total Transfers:</span>
                      <span className="font-semibold text-foreground">{transferData.stats.transfer_count}</span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Transfer History */}
        {transferData?.history && (
          <TransferHistory
            history={transferData.history}
            hasMore={transferData.has_more}
          />
        )}
      </div>
    </PanelLayout>
  )
}
