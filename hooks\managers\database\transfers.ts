/**
 * CythroDash - Transfers Database Management
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { Collection, UpdateFilter } from 'mongodb';
import { connectToDatabase } from '../../../database/index';
import { 
  CythroDashTransfer, 
  TransferStatus,
  TransferType,
  TransferSecurityFlags,
  TransferDeviceInfo,
  TransferMetadata,
  TransferHelpers,
  TRANSFERS_COLLECTION,
  TRANSFER_INDEXES
} from '../../../database/tables/cythro_dash_transfers';

// Create transfer data interface
export interface CreateTransferData {
  sender_user_id: number;
  receiver_user_id: number;
  amount: number;
  type: TransferType;
  status: TransferStatus;
  metadata?: TransferMetadata;
  security: TransferSecurityFlags;
  device_info: TransferDeviceInfo;
  sender_balance_before: number;
  sender_balance_after: number;
  receiver_balance_before: number;
  receiver_balance_after: number;
  processed_at?: Date;
  processed_by?: number;
  failure_reason?: string;
  created_by_ip: string;
  last_modified_by?: number;
  version: number;
}

// Update transfer data interface
export interface UpdateTransferData {
  status?: TransferStatus;
  processed_at?: Date;
  processed_by?: number;
  failure_reason?: string;
  last_modified_by?: number;
  updated_at?: Date;
  version?: number;
}

/**
 * Transfer Database Operations
 */
class TransferOperations {
  private collection: Collection<CythroDashTransfer> | null = null;
  private nextId = 1;

  /**
   * Get the transfers collection
   */
  private async getCollection(): Promise<Collection<CythroDashTransfer>> {
    if (!this.collection) {
      const db = await connectToDatabase();
      this.collection = db.collection<CythroDashTransfer>(TRANSFERS_COLLECTION);

      // Ensure indexes exist
      await this.ensureIndexes();

      // Initialize auto-increment ID
      await this.initializeAutoIncrement();
    }
    return this.collection;
  }

  /**
   * Ensure database indexes exist for optimal performance
   */
  private async ensureIndexes(): Promise<void> {
    try {
      const collection = await this.getCollection();

      // Create indexes for optimal query performance
      for (const index of TRANSFER_INDEXES) {
        try {
          await collection.createIndex(index as any);
        } catch (indexError) {
          console.warn('Index creation warning:', indexError);
        }
      }

      console.log('✅ Transfer indexes ensured');
    } catch (error) {
      console.error('❌ Error ensuring transfer indexes:', error);
    }
  }

  /**
   * Initialize auto-increment ID counter
   */
  private async initializeAutoIncrement(): Promise<void> {
    try {
      const collection = await this.getCollection();
      const lastTransfer = await collection.findOne({}, { sort: { id: -1 } });
      this.nextId = lastTransfer ? lastTransfer.id + 1 : 1;
    } catch (error) {
      console.error('❌ Error initializing auto-increment:', error);
      this.nextId = 1;
    }
  }

  /**
   * Get next auto-increment ID
   */
  private getNextId(): number {
    return this.nextId++;
  }

  /**
   * Create a new transfer
   */
  async createTransfer(data: CreateTransferData): Promise<CythroDashTransfer> {
    try {
      const collection = await this.getCollection();
      
      const transfer: CythroDashTransfer = {
        id: this.getNextId(),
        ...data,
        created_at: new Date(),
        updated_at: new Date()
      };

      const result = await collection.insertOne(transfer);
      
      if (!result.insertedId) {
        throw new Error('Failed to create transfer');
      }

      console.log('✅ Transfer created:', { id: transfer.id, amount: transfer.amount });
      return transfer;
    } catch (error) {
      console.error('❌ Error creating transfer:', error);
      throw error;
    }
  }

  /**
   * Get transfer by ID
   */
  async getTransferById(id: number): Promise<CythroDashTransfer | null> {
    try {
      const collection = await this.getCollection();
      return await collection.findOne({ id });
    } catch (error) {
      console.error('❌ Error getting transfer by ID:', error);
      return null;
    }
  }

  /**
   * Update transfer
   */
  async updateTransfer(id: number, updates: UpdateTransferData): Promise<CythroDashTransfer | null> {
    try {
      const collection = await this.getCollection();

      const updateData: UpdateFilter<CythroDashTransfer> = {
        $set: {
          ...updates,
          updated_at: new Date()
        }
      };

      const result = await collection.findOneAndUpdate(
        { id },
        updateData,
        { returnDocument: 'after' }
      );

      return result || null;
    } catch (error) {
      console.error('❌ Error updating transfer:', error);
      return null;
    }
  }

  /**
   * Get transfers by user (sent or received)
   */
  async getTransfersByUser(userId: number, limit = 50, offset = 0): Promise<CythroDashTransfer[]> {
    try {
      const collection = await this.getCollection();
      
      return await collection
        .find({
          $or: [
            { sender_user_id: userId },
            { receiver_user_id: userId }
          ]
        })
        .sort({ created_at: -1 })
        .skip(offset)
        .limit(limit)
        .toArray();
    } catch (error) {
      console.error('❌ Error getting transfers by user:', error);
      return [];
    }
  }

  /**
   * Get transfers between two users
   */
  async getTransfersBetweenUsers(senderId: number, receiverId: number, limit = 50): Promise<CythroDashTransfer[]> {
    try {
      const collection = await this.getCollection();
      
      return await collection
        .find({
          $or: [
            { sender_user_id: senderId, receiver_user_id: receiverId },
            { sender_user_id: receiverId, receiver_user_id: senderId }
          ]
        })
        .sort({ created_at: -1 })
        .limit(limit)
        .toArray();
    } catch (error) {
      console.error('❌ Error getting transfers between users:', error);
      return [];
    }
  }

  /**
   * Get transfers by status
   */
  async getTransfersByStatus(status: TransferStatus, limit = 50): Promise<CythroDashTransfer[]> {
    try {
      const collection = await this.getCollection();
      
      return await collection
        .find({ status })
        .sort({ created_at: -1 })
        .limit(limit)
        .toArray();
    } catch (error) {
      console.error('❌ Error getting transfers by status:', error);
      return [];
    }
  }

  /**
   * Get user transfer statistics
   */
  async getUserTransferStats(userId: number, days = 30): Promise<{
    total_sent: number;
    total_received: number;
    transfer_count: number;
    daily_sent: number;
    daily_received: number;
    daily_count: number;
  }> {
    try {
      const collection = await this.getCollection();
      const startDate = new Date(Date.now() - days * 24 * 60 * 60 * 1000);
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);

      // Get all transfers for the period
      const transfers = await collection
        .find({
          $or: [
            { sender_user_id: userId },
            { receiver_user_id: userId }
          ],
          created_at: { $gte: startDate },
          status: TransferStatus.COMPLETED
        })
        .toArray();

      // Calculate statistics
      let totalSent = 0;
      let totalReceived = 0;
      let dailySent = 0;
      let dailyReceived = 0;
      let dailyCount = 0;

      transfers.forEach(transfer => {
        const isToday = transfer.created_at >= todayStart;
        
        if (transfer.sender_user_id === userId) {
          totalSent += transfer.amount;
          if (isToday) {
            dailySent += transfer.amount;
            dailyCount++;
          }
        } else {
          totalReceived += transfer.amount;
          if (isToday) {
            dailyReceived += transfer.amount;
          }
        }
      });

      return {
        total_sent: totalSent,
        total_received: totalReceived,
        transfer_count: transfers.length,
        daily_sent: dailySent,
        daily_received: dailyReceived,
        daily_count: dailyCount
      };
    } catch (error) {
      console.error('❌ Error getting user transfer stats:', error);
      return {
        total_sent: 0,
        total_received: 0,
        transfer_count: 0,
        daily_sent: 0,
        daily_received: 0,
        daily_count: 0
      };
    }
  }

  /**
   * Validate daily transfer limits
   */
  async validateDailyLimits(userId: number, amount: number): Promise<boolean> {
    try {
      const todayStart = new Date();
      todayStart.setHours(0, 0, 0, 0);

      const collection = await this.getCollection();

      // Get today's transfers
      const todayTransfers = await collection
        .find({
          sender_user_id: userId,
          created_at: { $gte: todayStart },
          status: { $in: [TransferStatus.COMPLETED, TransferStatus.PENDING] }
        })
        .toArray();

      // Calculate daily totals
      const dailyAmount = todayTransfers.reduce((sum, t) => sum + t.amount, 0);
      const dailyCount = todayTransfers.length;

      // Check limits (configurable via environment or database)
      const maxDailyAmount = parseInt(process.env.MAX_DAILY_TRANSFER_AMOUNT || '100000');
      const maxDailyCount = parseInt(process.env.MAX_DAILY_TRANSFER_COUNT || '50');

      return (dailyAmount + amount <= maxDailyAmount) && (dailyCount < maxDailyCount);
    } catch (error) {
      console.error('❌ Error validating daily limits:', error);
      return false; // Fail safe
    }
  }

  /**
   * Check transfer cooldown
   */
  async checkTransferCooldown(userId: number): Promise<boolean> {
    try {
      const collection = await this.getCollection();
      const cooldownMinutes = parseInt(process.env.TRANSFER_COOLDOWN_MINUTES || '1');
      const cooldownTime = new Date(Date.now() - cooldownMinutes * 60 * 1000);

      const recentTransfer = await collection.findOne({
        sender_user_id: userId,
        created_at: { $gte: cooldownTime }
      });

      return !recentTransfer; // True if no recent transfer (cooldown passed)
    } catch (error) {
      console.error('❌ Error checking transfer cooldown:', error);
      return false; // Fail safe
    }
  }

  /**
   * Get flagged transfers for admin review
   */
  async getFlaggedTransfers(limit = 50): Promise<CythroDashTransfer[]> {
    try {
      const collection = await this.getCollection();

      return await collection
        .find({
          $or: [
            { 'security.is_suspicious': true },
            { 'security.requires_review': true },
            { status: TransferStatus.PENDING }
          ]
        })
        .sort({ created_at: -1 })
        .limit(limit)
        .toArray();
    } catch (error) {
      console.error('❌ Error getting flagged transfers:', error);
      return [];
    }
  }

  /**
   * Delete transfer (admin only)
   */
  async deleteTransfer(id: number): Promise<boolean> {
    try {
      const collection = await this.getCollection();
      const result = await collection.deleteOne({ id });
      return result.deletedCount === 1;
    } catch (error) {
      console.error('❌ Error deleting transfer:', error);
      return false;
    }
  }
}

// Export singleton instance
export const transferOperations = new TransferOperations();
