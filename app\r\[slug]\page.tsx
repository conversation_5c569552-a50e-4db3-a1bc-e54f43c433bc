"use client"

import { useEffect } from "react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import PanelLayout from "@/components/panel-layout"
import { useLinkStore } from "@/stores/link-store"
import { Button } from "@/components/ui/button"

export default function RedirectPage() {
  const params = useParams<{ slug: string }>()
  const router = useRouter()
  const links = useLinkStore((s) => s.links)

  useEffect(() => {
    const target = links.find((l) => l.slug === params.slug)?.target
    if (target) {
      window.location.href = target
    }
  }, [links, params.slug])

  const target = links.find((l) => l.slug === params.slug)?.target

  return (
    <PanelLayout title="Redirecting..." subtitle="Campaign link">
      <div className="glass-ultra rounded-2xl p-6 border border-border shadow-glass space-y-3">
        {target ? (
          <p className="text-muted-foreground">{'Taking you to: '}{target}</p>
        ) : (
          <>
            <p className="text-muted-foreground">{'Link not found or has been removed.'}</p>
            <Button className="premium-button" onClick={() => router.push("/")}>Go Home</Button>
          </>
        )}
      </div>
    </PanelLayout>
  )
}
