/**
 * CythroDash - Client-Safe Transfers Controller Wrapper
 *
 * This wrapper ensures that MongoDB-dependent operations only run on the server side,
 * preventing client-side import issues while maintaining the direct store-to-controller pattern.
 * 
 * When called from client-side, it returns mock/placeholder data to prevent errors
 * while maintaining the same interface.
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

// Import types only to avoid runtime imports
import type { 
  SendTransferRequest,
  ValidateTransferRequest,
  GetTransferHistoryRequest,
  GetTransferStatsRequest,
  TransferResponse,
  TransferValidationResponse
} from './Transfers';

/**
 * Client-safe wrapper for TransfersController
 * Loads the actual controller on server side, returns mock data on client side
 */
export class TransfersControllerClientSafe {
  
  private static async getController() {
    // Only import the actual controller on server side
    if (typeof window === 'undefined') {
      const { TransfersController } = await import('./Transfers');
      return TransfersController;
    }
    return null; // Return null for client-side calls
  }

  private static isClientSide(): boolean {
    return typeof window !== 'undefined';
  }

  /**
   * Send coins from one user to another
   */
  static async sendTransfer(request: SendTransferRequest): Promise<TransferResponse> {
    if (this.isClientSide()) {
      console.warn('⚠️ Send transfer called from client-side, returning mock response');
      return {
        success: false,
        message: 'Transfer processing requires server-side execution'
      };
    }

    try {
      const controller = await this.getController();
      if (controller) {
        return await controller.sendTransfer(request);
      }
      return {
        success: false,
        message: 'Controller not available'
      };
    } catch (error) {
      console.error('Server-side send transfer error:', error);
      return {
        success: false,
        message: 'Error processing transfer'
      };
    }
  }

  /**
   * Validate a transfer request
   */
  static async validateTransfer(request: ValidateTransferRequest): Promise<TransferValidationResponse> {
    if (this.isClientSide()) {
      console.warn('⚠️ Validate transfer called from client-side, returning mock response');
      // Basic client-side validation
      if (request.amount <= 0) {
        return {
          valid: false,
          message: 'Amount must be greater than 0'
        };
      }
      if (request.sender_user_id === request.receiver_user_id) {
        return {
          valid: false,
          message: 'Cannot transfer to yourself'
        };
      }
      return {
        valid: true,
        message: 'Basic validation passed (client-side)'
      };
    }

    try {
      const controller = await this.getController();
      if (controller) {
        return await controller.validateTransfer(request);
      }
      return {
        valid: false,
        message: 'Controller not available'
      };
    } catch (error) {
      console.error('Server-side validate transfer error:', error);
      return {
        valid: false,
        message: 'Error validating transfer'
      };
    }
  }

  /**
   * Get transfer history for a user
   */
  static async getTransferHistory(request: GetTransferHistoryRequest): Promise<TransferResponse> {
    if (this.isClientSide()) {
      console.warn('⚠️ Get transfer history called from client-side, returning mock data');
      return {
        success: true,
        message: 'Mock transfer history (client-side)',
        data: {
          transfers: [],
          total: 0,
          has_more: false
        }
      };
    }

    try {
      const controller = await this.getController();
      if (controller) {
        return await controller.getTransferHistory(request);
      }
      return {
        success: false,
        message: 'Controller not available'
      };
    } catch (error) {
      console.error('Server-side get transfer history error:', error);
      return {
        success: false,
        message: 'Error fetching transfer history'
      };
    }
  }

  /**
   * Get transfer statistics for a user
   */
  static async getTransferStats(request: GetTransferStatsRequest): Promise<TransferResponse> {
    if (this.isClientSide()) {
      console.warn('⚠️ Get transfer stats called from client-side, returning mock data');
      return {
        success: true,
        message: 'Mock transfer stats (client-side)',
        data: {
          total_sent: 0,
          total_received: 0,
          transfer_count: 0,
          daily_sent: 0,
          daily_received: 0,
          daily_count: 0
        }
      };
    }

    try {
      const controller = await this.getController();
      if (controller) {
        return await controller.getTransferStats(request);
      }
      return {
        success: false,
        message: 'Controller not available'
      };
    } catch (error) {
      console.error('Server-side get transfer stats error:', error);
      return {
        success: false,
        message: 'Error fetching transfer stats'
      };
    }
  }
}
