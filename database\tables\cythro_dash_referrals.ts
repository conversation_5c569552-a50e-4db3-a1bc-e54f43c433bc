/**
 * CythroDash - Referrals Database Schema
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { ObjectId } from 'mongodb';

// Referral action types
export enum ReferralActionType {
  CLICK = 'click',           // User clicked referral link (15 coins)
  SIGNUP = 'signup',         // User signed up via referral (30 coins)
  CLAIM = 'claim'            // User claimed their referral earnings
}

// Referral status
export enum ReferralStatus {
  PENDING = 'pending',       // Action completed but not yet claimed
  CLAIMED = 'claimed',       // Earnings have been claimed
  EXPIRED = 'expired'        // Referral expired (if applicable)
}

// Device information for security
export interface DeviceInfo {
  user_agent: string;
  browser?: string;
  os?: string;
  device_type?: 'desktop' | 'mobile' | 'tablet';
  screen_resolution?: string;
  timezone?: string;
}

// Location information for security
export interface LocationInfo {
  ip_address: string;
  country?: string;
  region?: string;
  city?: string;
  isp?: string;
}

// Referral activity interface
export interface CythroDashReferral {
  _id?: ObjectId;
  
  // Core referral data
  id: number;                           // Auto-increment ID
  referrer_user_id: number;            // User who owns the referral code
  referred_user_id?: number;           // User who was referred (null for clicks)
  referral_code: string;               // The referral code used
  
  // Action details
  action_type: ReferralActionType;     // Type of action (click, signup, claim)
  status: ReferralStatus;              // Current status
  coins_earned: number;                // Coins earned from this action
  
  // Security and fraud prevention
  ip_address: string;                  // IP address of the action
  device_info: DeviceInfo;             // Device information
  location_info?: Partial<LocationInfo>; // Location data for security
  
  // Fraud detection flags
  is_suspicious: boolean;              // Flagged as potentially fraudulent
  fraud_score: number;                 // Fraud risk score (0-100)
  fraud_reasons?: string[];            // Reasons for fraud suspicion
  
  // Session tracking
  session_id?: string;                 // Session identifier
  fingerprint_hash?: string;           // Device fingerprint hash
  
  // Validation and verification
  verified: boolean;                   // Whether the referral is verified
  verified_at?: Date;                  // When it was verified
  verified_by?: number;                // Admin who verified (if manual)
  
  // Claiming information
  claimed_at?: Date;                   // When earnings were claimed
  claim_transaction_id?: string;       // Transaction ID for the claim
  
  // Metadata
  created_at: Date;                    // When the action occurred
  updated_at: Date;                    // Last update time
  expires_at?: Date;                   // Expiration date (if applicable)
  
  // Additional tracking
  referrer_total_before: number;       // Referrer's total before this action
  referrer_total_after: number;        // Referrer's total after this action
  
  // Admin notes
  admin_notes?: string;                // Admin notes for manual review
  reviewed_by?: number;                // Admin who reviewed this referral
  reviewed_at?: Date;                  // When it was reviewed
}

// Referral statistics interface
export interface ReferralStats {
  total_clicks: number;
  total_signups: number;
  total_earnings: number;
  pending_earnings: number;
  claimed_earnings: number;
  conversion_rate: number;             // signups / clicks
  average_earnings_per_referral: number;
  last_activity?: Date;
}

// Fraud detection configuration
export interface FraudDetectionConfig {
  max_clicks_per_ip_per_day: number;
  max_signups_per_ip_per_day: number;
  min_time_between_actions: number;    // Milliseconds
  suspicious_user_agents: string[];
  blocked_countries?: string[];
  max_fraud_score: number;
}

// Helper functions for referral management
export const ReferralHelpers = {
  // Generate device fingerprint
  generateDeviceFingerprint: (deviceInfo: DeviceInfo, ipAddress: string): string => {
    const data = `${deviceInfo.user_agent}|${ipAddress}|${deviceInfo.screen_resolution || ''}|${deviceInfo.timezone || ''}`;
    return Buffer.from(data).toString('base64');
  },

  // Calculate fraud score based on various factors
  calculateFraudScore: (referral: Partial<CythroDashReferral>, recentActions: CythroDashReferral[]): number => {
    let score = 0;
    
    // Check for rapid actions from same IP
    const sameIpActions = recentActions.filter(r => r.ip_address === referral.ip_address);
    if (sameIpActions.length > 5) score += 30;
    
    // Check for same device fingerprint
    const sameDeviceActions = recentActions.filter(r => r.fingerprint_hash === referral.fingerprint_hash);
    if (sameDeviceActions.length > 3) score += 25;
    
    // Check time between actions
    if (recentActions.length > 0) {
      const lastAction = recentActions[0];
      const timeDiff = Date.now() - lastAction.created_at.getTime();
      if (timeDiff < 60000) score += 40; // Less than 1 minute
    }
    
    // Check for suspicious user agent patterns
    const userAgent = referral.device_info?.user_agent || '';
    if (userAgent.includes('bot') || userAgent.includes('crawler')) score += 50;
    
    return Math.min(score, 100);
  },

  // Check if referral is eligible for rewards
  isEligibleForReward: (referral: CythroDashReferral): boolean => {
    return !referral.is_suspicious && 
           referral.fraud_score < 70 && 
           referral.verified &&
           referral.status === ReferralStatus.PENDING;
  },

  // Get default fraud detection config
  getDefaultFraudConfig: (): FraudDetectionConfig => ({
    max_clicks_per_ip_per_day: 10,
    max_signups_per_ip_per_day: 2,
    min_time_between_actions: 30000, // 30 seconds
    suspicious_user_agents: ['bot', 'crawler', 'spider', 'scraper'],
    max_fraud_score: 70
  }),

  // Get reward amount based on action type
  getRewardAmount: (actionType: ReferralActionType): number => {
    switch (actionType) {
      case ReferralActionType.CLICK:
        return 15;
      case ReferralActionType.SIGNUP:
        return 30;
      default:
        return 0;
    }
  },

  // Validate referral code format
  // Format: USERNAME(4)TIMESTAMP(5-6)RANDOM(6-8) = 15-18 characters
  isValidReferralCode: (code: string): boolean => {
    // Allow 10-25 characters to accommodate various generation patterns
    // Must be uppercase alphanumeric only
    return /^[A-Z0-9]{10,25}$/.test(code);
  }
};

// Export collection name for consistency
export const REFERRALS_COLLECTION = 'cythro_dash_referrals';

// Database indexes for optimal performance
export const REFERRALS_INDEXES = [
  { key: { id: 1 }, name: 'id_unique', unique: true },
  { key: { referrer_user_id: 1 }, name: 'referrer_user_id_index' },
  { key: { referred_user_id: 1 }, name: 'referred_user_id_index' },
  { key: { referral_code: 1 }, name: 'referral_code_index' },
  { key: { action_type: 1 }, name: 'action_type_index' },
  { key: { status: 1 }, name: 'status_index' },
  { key: { ip_address: 1 }, name: 'ip_address_index' },
  { key: { fingerprint_hash: 1 }, name: 'fingerprint_hash_index' },
  { key: { is_suspicious: 1 }, name: 'suspicious_index' },
  { key: { verified: 1 }, name: 'verified_index' },
  { key: { created_at: -1 }, name: 'created_at_desc_index' },
  { key: { referrer_user_id: 1, created_at: -1 }, name: 'referrer_activity_index' },
  { key: { ip_address: 1, created_at: -1 }, name: 'ip_activity_index' },
  { key: { fingerprint_hash: 1, created_at: -1 }, name: 'device_activity_index' },
  { key: { status: 1, referrer_user_id: 1 }, name: 'status_referrer_index' }
];
