"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Server, ChevronLeft, ChevronRight, Home, Wallet, Gift, Share2, Send, PlusSquare, X, Users } from "lucide-react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { useAppSettings, THEMES } from "@/stores/settings"

export default function PterodactylSidebar({
  sidebarOpen,
  setSidebarOpen,
}: { sidebarOpen: boolean; setSidebarOpen: (open: boolean) => void }) {
  const pathname = usePathname()
  const router = useRouter()
  const [collapsed, setCollapsed] = useState(false)
  const { logoDataUrl, currentTheme } = useAppSettings()

  // Get current theme colors
  const themeColors = THEMES[currentTheme]

  const items = [
    { icon: Home, label: "Dashboard", href: "/" },
    { icon: Wallet, label: "Wallet", href: "/wallet" },
    { icon: Gift, label: "Earn", href: "/earn" },
    { icon: Share2, label: "Referrals", href: "/referrals" },
    { icon: PlusSquare, label: "Redeem", href: "/redeem" },
    { icon: Send, label: "Transfer", href: "/transfer" },
    { icon: Server, label: "Servers", href: "/servers" },
    { icon: PlusSquare, label: "Create Server", href: "/create-server" },
    { icon: Users, label: "Admin", href: "/admin" },
  ]

  // Handle mobile navigation with proper cleanup
  const handleMobileNavigation = (href: string, e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    // Close sidebar first
    setSidebarOpen(false)

    // Navigate after a small delay to ensure sidebar closes
    setTimeout(() => {
      router.push(href)
    }, 150)
  }

  // Prevent body scroll when sidebar is open
  useEffect(() => {
    if (sidebarOpen) {
      document.body.style.overflow = "hidden"
      document.body.style.paddingRight = "0px"
    } else {
      document.body.style.overflow = ""
      document.body.style.paddingRight = ""
    }

    return () => {
      document.body.style.overflow = ""
      document.body.style.paddingRight = ""
    }
  }, [sidebarOpen])

  const renderNav = (isMobile?: boolean) => (
    <nav className="p-4 space-y-2">
      {items.map((item) => {
        const active = pathname === item.href || (item.href !== "/" && pathname.startsWith(item.href))

        // Dynamic styles based on current theme
        const activeStyles = {
          background: `linear-gradient(135deg, ${themeColors.colors.brandFrom}25, ${themeColors.colors.brandTo}25)`,
          borderColor: `${themeColors.colors.brandFrom}50`,
          color: themeColors.colors.foreground
        }

        if (isMobile) {
          return (
            <div
              key={item.label}
              onClick={(e) => handleMobileNavigation(item.href, e)}
              className={`flex items-center gap-3 px-4 py-3.5 rounded-xl transition-all duration-200 group relative cursor-pointer border ${
                active
                  ? "shadow-lg backdrop-blur-sm"
                  : "text-muted-foreground hover:bg-accent/10 hover:text-foreground hover:shadow-md hover:border hover:border-border/50"
              }`}
              style={active ? activeStyles : {}}
            >
              <item.icon className={`w-5 h-5 flex-shrink-0 ${active ? "" : "group-hover:text-foreground"}`}
                style={active ? { color: themeColors.colors.foreground } : {}} />
              <span
                className={`${active ? "font-medium" : "group-hover:text-foreground"} font-medium truncate`}
                style={active ? { color: themeColors.colors.foreground } : {}}
              >
                {item.label}
              </span>
              {active && (
                <div
                  className="ml-auto w-2 h-2 rounded-full shadow-sm animate-pulse"
                  style={{ backgroundColor: themeColors.colors.brandFrom }}
                />
              )}
            </div>
          )
        }

        return (
          <Link
            key={item.label}
            href={item.href}
            className={`flex items-center gap-3 px-4 py-3.5 rounded-xl transition-all duration-200 group relative border ${
              active
                ? "shadow-lg backdrop-blur-sm"
                : "text-muted-foreground hover:bg-accent/10 hover:text-foreground hover:shadow-md hover:border hover:border-border/50"
            }`}
            style={active ? activeStyles : {}}
          >
            <item.icon className={`w-5 h-5 flex-shrink-0 ${active ? "" : "group-hover:text-foreground"}`}
              style={active ? { color: themeColors.colors.foreground } : {}} />
            {!collapsed && (
              <span
                className={`${active ? "font-medium" : "group-hover:text-foreground"} font-medium truncate`}
                style={active ? { color: themeColors.colors.foreground } : {}}
              >
                {item.label}
              </span>
            )}
            {active && !collapsed && (
              <div
                className="ml-auto w-2 h-2 rounded-full shadow-sm animate-pulse"
                style={{ backgroundColor: themeColors.colors.brandFrom }}
              />
            )}
          </Link>
        )
      })}
    </nav>
  )

  return (
    <>
      {/* Desktop Sidebar */}
      <aside className={`${collapsed ? "w-16" : "w-64"} transition-[width] duration-300 relative z-20 hidden lg:block`}>
        <div className="h-svh glass-ultra border-r border-border shadow-glass">
          <div className="p-4 border-b border-border/50">
            <div className="flex items-center justify-between">
              {!collapsed && (
                <div className="flex items-center gap-3">
                  {logoDataUrl ? (
                    <img
                      src={logoDataUrl || "/placeholder.svg"}
                      alt="Brand logo"
                      className="w-10 h-10 rounded-xl object-cover border border-border shadow-glass"
                    />
                  ) : (
                    <div
                      className="w-10 h-10 rounded-xl flex items-center justify-center shadow-lg"
                      style={{
                        background: `linear-gradient(135deg, ${themeColors.colors.brandFrom}, ${themeColors.colors.brandTo})`
                      }}
                    >
                      <Server className="w-5 h-5 text-white" />
                    </div>
                  )}
                  <div>
                    <h1 className="text-xl font-black brand-heading"> {process.env.NEXT_PUBLIC_NAME}</h1>
                    <p className="text-muted-foreground text-xs -mt-0.5">APP_VERSION</p>
                  </div>
                </div>
              )}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setCollapsed(!collapsed)}
                className="glass-button p-2 hover:bg-accent/20"
                aria-label={collapsed ? "Expand sidebar" : "Collapse sidebar"}
              >
                {collapsed ? <ChevronRight className="w-4 h-4" /> : <ChevronLeft className="w-4 h-4" />}
              </Button>
            </div>
          </div>
          {renderNav()}
        </div>
      </aside>

      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div className="lg:hidden">
          {/* Backdrop */}
          <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-[998]" onClick={() => setSidebarOpen(false)} />

          {/* Sidebar Panel */}
          <div className="fixed left-0 top-0 bottom-0 w-[280px] bg-background border-r border-border shadow-2xl z-[999] transform translate-x-0">
            {/* Header */}
            <div className="p-4 border-b border-border/50 bg-background">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  {logoDataUrl ? (
                    <img
                      src={logoDataUrl || "/placeholder.svg"}
                      alt="Brand logo"
                      className="w-9 h-9 rounded-xl object-cover border border-border shadow-glass"
                    />
                  ) : (
                    <div
                      className="w-9 h-9 rounded-xl flex items-center justify-center shadow-glass"
                      style={{
                        background: `linear-gradient(135deg, ${themeColors.colors.brandFrom}, ${themeColors.colors.brandTo})`
                      }}
                    >
                      <Server className="w-5 h-5 text-white" />
                    </div>
                  )}
                  <div>
                    <h1 className="text-lg font-black brand-heading">Pterodash</h1>
                    <p className="text-muted-foreground text-xs -mt-0.5">Game Panel</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSidebarOpen(false)}
                  className="glass-button p-2 hover:bg-accent/20 -mr-2"
                  aria-label="Close sidebar"
                >
                  <X className="w-5 h-5" />
                </Button>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex-1 overflow-y-auto bg-background">{renderNav(true)}</div>
          </div>
        </div>
      )}
    </>
  )
}
