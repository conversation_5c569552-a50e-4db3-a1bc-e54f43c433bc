/**
 * CythroDash - Referral Stats API Route
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { authMiddleware } from '@/lib/auth/middleware';
import { ReferralsController } from '@/hooks/managers/controller/User/Referrals';

export async function GET(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await authMiddleware(request);
    if (!authResult.success) {
      return authResult.response!;
    }

    const user = authResult.user;

    // Get referral statistics using the controller
    const statsResponse = await ReferralsController.getReferralStats({
      user_id: user.id
    });

    if (!statsResponse.success) {
      return NextResponse.json(
        {
          success: false,
          message: statsResponse.message,
          errors: statsResponse.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: statsResponse.message,
      data: statsResponse.data
    });

  } catch (error) {
    console.error('Referral stats API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'An unexpected error occurred while fetching referral stats',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}
