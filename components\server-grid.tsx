"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import {
  ServerIcon,
  MoreHorizontal,
  Play,
  Square,
  RotateCcw,
  Settings,
  Trash2,
  ExternalLink,
  Users,
  Cpu,
  HardDrive,
  Clock,
} from "lucide-react"
import { useServerStore, type Server } from "@/stores/server-store"
import { useAppSettings } from "@/stores/settings"
import { DeleteConfirmationDialog } from "./delete-confirmation-dialog"

type Props = {
  onServerSelect?: (server: Server) => void
}

export default function ServerGrid({ onServerSelect }: Props) {
  const servers = useServerStore((s) => s.servers)
  const remove = useServerStore((s) => s.remove)
  const { panelUrl } = useAppSettings()

  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean
    server: Server | null
  }>({ open: false, server: null })

  const handleDeleteClick = (server: Server, e: React.MouseEvent) => {
    e.stopPropagation()
    setDeleteDialog({ open: true, server })
  }

  const handleDeleteConfirm = () => {
    if (deleteDialog.server) {
      remove(deleteDialog.server.id)
      setDeleteDialog({ open: false, server: null })
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case "online":
        return "bg-emerald-500/20 text-emerald-400 border-emerald-500/30"
      case "offline":
        return "bg-red-500/20 text-red-400 border-red-500/30"
      case "starting":
        return "bg-yellow-500/20 text-yellow-400 border-yellow-500/30"
      case "stopping":
        return "bg-orange-500/20 text-orange-400 border-orange-500/30"
      default:
        return "bg-gray-500/20 text-gray-400 border-gray-500/30"
    }
  }

  const getStatusDot = (status: string) => {
    switch (status) {
      case "online":
        return "bg-emerald-400"
      case "offline":
        return "bg-red-400"
      case "starting":
        return "bg-yellow-400"
      case "stopping":
        return "bg-orange-400"
      default:
        return "bg-gray-400"
    }
  }

  if (servers.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="w-16 h-16 mx-auto mb-4 rounded-2xl bg-gradient-to-br from-gray-500/20 to-gray-600/20 flex items-center justify-center">
          <ServerIcon className="w-8 h-8 text-muted-foreground" />
        </div>
        <h3 className="text-lg font-semibold text-foreground mb-2">No servers yet</h3>
        <p className="text-muted-foreground mb-6">Create your first server to get started!</p>
        <Button className="premium-button">
          <ServerIcon className="w-4 h-4 mr-2" />
          Create Server
        </Button>
      </div>
    )
  }

  return (
    <>
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6">
        {servers.map((server) => (
          <Card
            key={server.id}
            className="glass-ultra border border-border hover:shadow-glass-lg transition-all duration-300 rounded-2xl overflow-hidden group cursor-pointer"
            onClick={() => onServerSelect?.(server)}
          >
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3 min-w-0 flex-1">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center shadow-lg flex-shrink-0">
                    <ServerIcon className="w-6 h-6 text-white" />
                  </div>
                  <div className="min-w-0 flex-1">
                    <CardTitle className="text-foreground text-lg font-bold truncate">{server.name}</CardTitle>
                    <div className="flex items-center gap-2 mt-1">
                      <div className={`w-2 h-2 rounded-full ${getStatusDot(server.status)} animate-pulse`} />
                      <span className="text-sm text-muted-foreground capitalize">{server.status}</span>
                      <span className="text-muted-foreground">•</span>
                      <span className="text-sm text-muted-foreground">{server.type}</span>
                    </div>
                  </div>
                </div>
                <div className="flex items-center gap-1 opacity-0 group-hover:opacity-100 transition-opacity">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0 hover:bg-accent"
                    onClick={(e) => {
                      e.stopPropagation()
                      const url = panelUrl || "https://panel.example.com"
                      window.open(url, "_blank")
                    }}
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Button>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0 hover:bg-accent">
                        <MoreHorizontal className="w-4 h-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="glass-ultra border-border">
                      <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                        <Settings className="w-4 h-4 mr-2" />
                        Manage
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={(e) => e.stopPropagation()}>
                        <ExternalLink className="w-4 h-4 mr-2" />
                        Open Panel
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        onClick={(e) => handleDeleteClick(server, e)}
                        className="text-red-400 focus:text-red-400"
                      >
                        <Trash2 className="w-4 h-4 mr-2" />
                        Delete
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Server Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-blue-400" />
                      <span className="text-sm text-muted-foreground">Players</span>
                    </div>
                    <span className="text-sm font-semibold text-foreground">{server.players}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Cpu className="w-4 h-4 text-emerald-400" />
                      <span className="text-sm text-muted-foreground">CPU</span>
                    </div>
                    <span className="text-sm font-semibold text-foreground">{server.cpu}</span>
                  </div>
                </div>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <HardDrive className="w-4 h-4 text-purple-400" />
                      <span className="text-sm text-muted-foreground">Memory</span>
                    </div>
                    <span className="text-sm font-semibold text-foreground">{server.memory}</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-amber-400" />
                      <span className="text-sm text-muted-foreground">Uptime</span>
                    </div>
                    <span className="text-sm font-semibold text-foreground">{server.uptime}</span>
                  </div>
                </div>
              </div>

              {/* Status Badge */}
              <div className="flex justify-center">
                <Badge className={getStatusColor(server.status)}>
                  {server.status.charAt(0).toUpperCase() + server.status.slice(1)}
                </Badge>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2 pt-2">
                {server.status === "online" ? (
                  <Button
                    size="sm"
                    variant="outline"
                    className="flex-1 secondary-button bg-transparent hover:bg-red-500/10 hover:text-red-400 hover:border-red-500/30"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <Square className="w-4 h-4 mr-2" />
                    Stop
                  </Button>
                ) : (
                  <Button size="sm" className="flex-1 premium-button" onClick={(e) => e.stopPropagation()}>
                    <Play className="w-4 h-4 mr-2" />
                    Start
                  </Button>
                )}
                <Button
                  size="sm"
                  variant="outline"
                  className="secondary-button bg-transparent hover:bg-amber-500/10 hover:text-amber-400 hover:border-amber-500/30"
                  onClick={(e) => e.stopPropagation()}
                >
                  <RotateCcw className="w-4 h-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Delete Confirmation Dialog */}
      <DeleteConfirmationDialog
        open={deleteDialog.open}
        onOpenChange={(open) => setDeleteDialog({ open, server: null })}
        onConfirm={handleDeleteConfirm}
        title="Delete Server"
        description="Are you sure you want to delete this server? This action cannot be undone and all server data will be permanently lost."
        itemName={deleteDialog.server?.name}
        destructive={true}
      />
    </>
  )
}
