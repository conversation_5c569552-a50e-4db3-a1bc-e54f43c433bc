"use client"

import { Award, Trophy, Crown, Gem } from "lucide-react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { getAllTiers } from "@/lib/referral-tiers"
import { ReferralData } from "@/stores/referral-store"

interface TierProgressionProps {
  referralData: ReferralData
}

// Icon mapping function to convert icon names to React components
const getIconComponent = (iconName: string) => {
  const iconMap: Record<string, any> = {
    Trophy,
    Award,
    Crown,
    Gem
  }
  return iconMap[iconName] || Trophy
}

export default function TierProgression({ referralData }: TierProgressionProps) {
  const allTiers = getAllTiers()

  // Handle case where tier data might not be available
  const currentTier = referralData.stats.current_tier
  if (!currentTier) {
    return (
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="flex items-center gap-3 text-lg">
            <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center">
              <Award className="w-4 h-4 text-white" />
            </div>
            Loading Tiers...
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            {[1, 2, 3, 4].map(i => (
              <div key={i} className="h-16 bg-gray-200 rounded"></div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  const currentTierIndex = allTiers.findIndex(tier => tier.id === currentTier.id)

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="flex items-center gap-3 text-lg">
          <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-amber-500 to-orange-600 flex items-center justify-center">
            <Award className="w-4 h-4 text-white" />
          </div>
          All Tiers
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          {allTiers.map((tier, index) => {
            const IconComponent = getIconComponent(tier.iconName || 'Trophy')
            const isCurrentTier = tier.id === currentTier.id
            const isUnlocked = index <= currentTierIndex

            return (
              <div
                key={tier.name}
                className={`p-4 rounded-xl border transition-all ${
                  isCurrentTier
                    ? 'glass-ultra border-[rgb(20,136,204)]/50 bg-[rgb(20,136,204)]/5'
                    : isUnlocked
                    ? 'glass-ultra border-border/50'
                    : 'bg-muted/30 border-border/30 opacity-60'
                }`}
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    <div
                      className={`w-6 h-6 rounded-lg bg-gradient-to-br ${tier.color} flex items-center justify-center`}
                    >
                      <IconComponent className="w-3 h-3 text-white" />
                    </div>
                    <span className="font-medium text-foreground">{tier.name}</span>
                    {isCurrentTier && (
                      <Badge className="bg-emerald-400/10 text-emerald-400 border-emerald-400/20 text-xs">
                        Current
                      </Badge>
                    )}
                  </div>
                  <span className="text-sm font-bold text-foreground">+{tier.bonusPercentage}%</span>
                </div>
                <p className="text-xs text-muted-foreground">
                  {tier.minReferrals === 0 
                    ? "Starting tier" 
                    : `${tier.minReferrals}+ successful referrals`
                  }
                </p>
              </div>
            )
          })}
        </div>
      </CardContent>
    </Card>
  )
}
