"use client"

import { useEffect } from "react"
import PanelLayout from "@/components/panel-layout"
import { useToast } from "@/hooks/use-toast"

import { useReferralStore } from "@/stores/referral-store"

// Import the new components
import ReferralStatsOverview from "@/components/referrals/referral-stats-overview"
import ReferralLinkSharing from "@/components/referrals/referral-link-sharing"
import TierStatus from "@/components/referrals/tier-status"
import RecentActivity from "@/components/referrals/recent-activity"
import HowItWorks from "@/components/referrals/how-it-works"
import TierProgression from "@/components/referrals/tier-progression"



export default function ReferralsPage() {
  const { toast } = useToast()
  const {
    referralData,
    error,
    fetchReferralStats,
    claimEarnings,
    clearError
  } = useReferralStore()

  // Simplified data fetching - no authentication checks needed
  useEffect(() => {
    fetchReferralStats()
  }, [fetchReferralStats])

  // Show error toast when error changes
  useEffect(() => {
    if (error) {
      toast({
        title: "Error",
        description: error,
        variant: "destructive"
      })
      clearError()
    }
  }, [error, toast, clearError])

  // Handle claim earnings
  const handleClaimEarnings = async () => {
    if (!referralData?.stats.pending_earnings || referralData.stats.pending_earnings <= 0) {
      toast({
        title: "No Earnings",
        description: "You have no pending earnings to claim",
        variant: "destructive"
      })
      return
    }

    const result = await claimEarnings()

    if (result.success) {
      toast({
        title: "Success!",
        description: result.message || `Claimed ${result.coins_claimed || 0} coins successfully`,
      })
    } else {
      toast({
        title: "Error",
        description: result.message || "Failed to claim earnings",
        variant: "destructive"
      })
    }
  }







  return (
    <PanelLayout title="Referral Program" subtitle="Invite friends and earn coins together">
      <div className="space-y-8">
        {referralData ? (
          <>
            {/* Stats Overview */}
            <ReferralStatsOverview referralData={referralData} />

            {/* Main Content Grid */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              {/* Left Column - Main Actions */}
              <div className="lg:col-span-2 space-y-6">
                <ReferralLinkSharing
                  referralData={referralData}
                  onClaimEarnings={handleClaimEarnings}
                />
                <HowItWorks />
                <RecentActivity referralData={referralData} />
              </div>

              {/* Right Column - Tier Information */}
              <div className="space-y-6">
                <TierStatus referralData={referralData} />
                <TierProgression referralData={referralData} />
              </div>
            </div>
          </>
        ) : (
          <div className="text-center py-8">
            <p className="text-muted-foreground">Loading referral data...</p>
          </div>
        )}
      </div>
    </PanelLayout>
  )
}
