"use client"


import { But<PERSON> } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

import { ChevronDown, LogOut, Settings, User, Mail, Shield, Coins } from "lucide-react"
import { useRouter } from "next/navigation"
import { useAuthStore } from "@/stores/user-store"
import { useToast } from "@/hooks/use-toast"

export default function UserMenu() {
  const router = useRouter()
  const { toast } = useToast()
  const { currentUser, logout, isAuthenticated } = useAuthStore()

  // Don't render if not authenticated, but don't redirect from here
  // Let the AuthWrapper handle redirects
  if (!isAuthenticated || !currentUser) {
    return null
  }

  const handleLogout = async () => {
    try {
      await logout()
      toast({
        title: "Logged out",
        description: "You have been successfully logged out.",
      })
      router.push('/auth/login')
    } catch (error) {
      console.error('Logout error:', error)
      toast({
        title: "Logout Error",
        description: "There was an error logging out. Please try again.",
        variant: "destructive"
      })
    }
  }

  const displayName = `${currentUser.first_name} ${currentUser.last_name}`
  const roleText = currentUser.role === 0 ? "Administrator" : "User"

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="sm" className="glass-button pl-1 pr-2 h-9" aria-label="Open user menu">
          <div className="w-7 h-7 rounded-full bg-brand mr-2 border border-border shadow-glass flex items-center justify-center">
            {currentUser.avatar_url ? (
              <img src={currentUser.avatar_url} alt="Avatar" className="w-full h-full rounded-full object-cover" />
            ) : (
              <User className="w-4 h-4 text-white" />
            )}
          </div>
          <span className="hidden md:inline text-sm text-foreground">{displayName}</span>
          <div className="hidden md:flex items-center ml-2 px-2 py-1 bg-brand/20 rounded-full">
            <Coins className="w-3 h-3 text-brand mr-1" />
            <span className="text-xs font-medium text-brand">{currentUser.coins.toLocaleString()}</span>
          </div>
          <ChevronDown className="w-4 h-4 ml-1 text-muted-foreground" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-72 glass-ultra border border-border shadow-glass">
        {/* Profile Section */}
        <DropdownMenuLabel className="p-0">
          <div className="flex items-center gap-3 p-3">
            <div className="w-12 h-12 rounded-full bg-brand border border-border shadow-glass flex items-center justify-center">
              {currentUser.avatar_url ? (
                <img src={currentUser.avatar_url} alt="Avatar" className="w-full h-full rounded-full object-cover" />
              ) : (
                <User className="w-6 h-6 text-white" />
              )}
            </div>
            <div className="min-w-0 flex-1">
              <p className="text-foreground font-semibold truncate">{displayName}</p>
              <p className="text-xs text-muted-foreground truncate flex items-center gap-1">
                <Mail className="w-3 h-3" />
                {currentUser.email}
              </p>
              <p className="text-xs text-muted-foreground flex items-center gap-1 mt-1">
                <Shield className="w-3 h-3" />
                {roleText}
              </p>
            </div>
           
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        <DropdownMenuItem className="gap-2" onClick={() => router.push("/profile")}>
          <User className="w-4 h-4" />
          View Profile
        </DropdownMenuItem>

        <DropdownMenuItem className="gap-2" onClick={() => router.push("/settings")}>
          <Settings className="w-4 h-4" />
          Settings
        </DropdownMenuItem>

        <DropdownMenuItem className="gap-2" onClick={() => router.push("/wallet")}>
          <Coins className="w-4 h-4" />
          Wallet
        </DropdownMenuItem>

        {currentUser.role === 0 && (
          <>
            <DropdownMenuSeparator />
            <DropdownMenuItem className="gap-2" onClick={() => router.push("/admin")}>
              <Shield className="w-4 h-4" />
              Admin Panel
            </DropdownMenuItem>
          </>
        )}

        <DropdownMenuSeparator />

        <DropdownMenuItem className="gap-2 text-red-400 focus:text-red-300" onClick={handleLogout}>
          <LogOut className="w-4 h-4" />
          Sign out
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
