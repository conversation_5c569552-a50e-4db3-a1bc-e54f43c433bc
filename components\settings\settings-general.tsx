"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Save, Upload, User, Mail, Globe, Clock, MapPin } from 'lucide-react'
import { useAuthStore } from "@/stores/user-store"
import { useAppSettings } from "@/stores/settings"
import { useToast } from "@/hooks/use-toast"

export default function SettingsGeneral() {
  const { currentUser, updateUserData, updateUserProfile } = useAuthStore()
  const { language, setLanguage, timezone, setTimezone } = useAppSettings()
  const { toast } = useToast()

  const [formData, setFormData] = useState({
    first_name: currentUser?.first_name || "",
    last_name: currentUser?.last_name || "",
    username: currentUser?.username || "",
    email: currentUser?.email || "",
    display_name: currentUser?.display_name || "",
    bio: "",
    website: "",
    social_links: {
      twitter: "",
      discord: "",
      github: ""
    }
  })

  const [isLoading, setIsLoading] = useState(false)

  // Update form data when currentUser changes
  useEffect(() => {
    if (currentUser) {
      setFormData(prev => ({
        ...prev,
        first_name: currentUser.first_name || "",
        last_name: currentUser.last_name || "",
        username: currentUser.username || "",
        email: currentUser.email || "",
        display_name: currentUser.display_name || "",
      }))
    }
  }, [currentUser])

  const handleInputChange = (field: string, value: string) => {
    if (field.includes('.')) {
      // Handle nested fields like social_links.twitter
      const [parent, child] = field.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent as keyof typeof prev],
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
    }
  }

  const handleSave = async () => {
    setIsLoading(true)
    try {
      console.log('Starting profile update...')

      // Prepare update data with all form fields
      const updateData = {
        username: formData.username,
        email: formData.email,
        first_name: formData.first_name,
        last_name: formData.last_name,
        display_name: formData.display_name,
        bio: formData.bio,
        website: formData.website,
        timezone: timezone,
        language: language,
        social_links: formData.social_links
      }

      console.log('Update data:', updateData)

      // Update user profile via API (this will save to database and sync to Pterodactyl)
      const result = await updateUserProfile(updateData)

      console.log('Profile update result:', result)

      if (result.success) {
        // Also update language and timezone in app settings
        setLanguage(language)
        setTimezone(timezone)

        toast({
          title: "Profile Updated",
          description: result.message || "Your profile has been saved to the database and synced to Pterodactyl.",
        })
      } else {
        // Show specific error message
        const errorMessage = result.errors && result.errors.length > 0
          ? result.errors.map(err => `${err.field}: ${err.message}`).join(', ')
          : result.message || "Failed to update profile. Please try again."

        toast({
          title: "Update Failed",
          description: errorMessage,
          variant: "destructive"
        })
      }
    } catch (error) {
      console.error('Profile update error:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred. Please try again.",
        variant: "destructive"
      })
    } finally {
      setIsLoading(false)
    }
  }

  const timezones = [
    "UTC",
    "America/New_York",
    "America/Los_Angeles",
    "Europe/London",
    "Europe/Paris",
    "Asia/Tokyo",
    "Asia/Shanghai",
    "Australia/Sydney"
  ]

  const languages = [
    { code: "en", name: "English" },
    { code: "es", name: "Español" },
    { code: "fr", name: "Français" },
    { code: "de", name: "Deutsch" },
    { code: "it", name: "Italiano" },
    { code: "pt", name: "Português" },
    { code: "ru", name: "Русский" },
    { code: "ja", name: "日本語" },
    { code: "ko", name: "한국어" },
    { code: "zh", name: "中文" }
  ]



  return (
    <div className="space-y-6">
      {/* Profile Information */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="text-foreground text-xl flex items-center gap-2">
            <User className="w-5 h-5" />
            Profile Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Avatar Section */}
          <div className="flex items-center gap-6">
            <Avatar className="w-20 h-20">
              <AvatarImage src={currentUser?.avatar_url} />
              <AvatarFallback className="text-lg">
                {currentUser?.first_name?.[0]}{currentUser?.last_name?.[0]}
              </AvatarFallback>
            </Avatar>
            <div className="space-y-2">
              <Button variant="outline" className="secondary-button">
                <Upload className="w-4 h-4 mr-2" />
                Upload Avatar
              </Button>
              <p className="text-sm text-muted-foreground">
                JPG, PNG or GIF. Max size 2MB.
              </p>
            </div>
          </div>

          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-foreground">First Name</Label>
              <Input
                value={formData.first_name}
                onChange={(e) => handleInputChange("first_name", e.target.value)}
                className="glass-ultra border-border"
                placeholder="Enter your first name"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-foreground">Last Name</Label>
              <Input
                value={formData.last_name}
                onChange={(e) => handleInputChange("last_name", e.target.value)}
                className="glass-ultra border-border"
                placeholder="Enter your last name"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-foreground">Username</Label>
              <Input
                value={formData.username}
                onChange={(e) => handleInputChange("username", e.target.value)}
                className="glass-ultra border-border"
                placeholder="Enter your username"
              />
            </div>
            <div className="space-y-2">
              <Label className="text-foreground flex items-center gap-2">
                <Mail className="w-4 h-4" />
                Email Address
              </Label>
              <Input
                value={formData.email}
                onChange={(e) => handleInputChange("email", e.target.value)}
                className="glass-ultra border-border"
                placeholder="Enter your email"
                type="email"
              />
            </div>
          </div>

          {/* Display Name */}
          <div className="space-y-2">
            <Label className="text-foreground">Display Name</Label>
            <Input
              value={formData.display_name}
              onChange={(e) => handleInputChange("display_name", e.target.value)}
              className="glass-ultra border-border"
              placeholder="How others will see your name"
            />
          </div>

          {/* Bio */}
          <div className="space-y-2">
            <Label className="text-foreground">Bio</Label>
            <Textarea
              value={formData.bio}
              onChange={(e) => handleInputChange("bio", e.target.value)}
              className="glass-ultra border-border"
              placeholder="Tell us about yourself..."
              rows={3}
            />
          </div>

          {/* Website */}
          <div className="space-y-2">
            <Label className="text-foreground flex items-center gap-2">
              <Globe className="w-4 h-4" />
              Website
            </Label>
            <Input
              value={formData.website}
              onChange={(e) => handleInputChange("website", e.target.value)}
              className="glass-ultra border-border"
              placeholder="https://yourwebsite.com"
              type="url"
            />
          </div>

          {/* Social Links */}
          <div className="space-y-4">
            <Label className="text-foreground text-lg font-semibold">Social Links</Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label className="text-foreground text-sm">Twitter</Label>
                <Input
                  value={formData.social_links.twitter}
                  onChange={(e) => handleInputChange("social_links.twitter", e.target.value)}
                  className="glass-ultra border-border"
                  placeholder="@username"
                />
              </div>
              <div className="space-y-2">
                <Label className="text-foreground text-sm">Discord</Label>
                <Input
                  value={formData.social_links.discord}
                  onChange={(e) => handleInputChange("social_links.discord", e.target.value)}
                  className="glass-ultra border-border"
                  placeholder="username#1234"
                />
              </div>
              <div className="space-y-2">
                <Label className="text-foreground text-sm">GitHub</Label>
                <Input
                  value={formData.social_links.github}
                  onChange={(e) => handleInputChange("social_links.github", e.target.value)}
                  className="glass-ultra border-border"
                  placeholder="username"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Preferences */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="text-foreground text-xl">Preferences</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-2">
              <Label className="text-foreground flex items-center gap-2">
                <Globe className="w-4 h-4" />
                Language
              </Label>
              <Select value={language} onValueChange={setLanguage}>
                <SelectTrigger className="glass-ultra border-border">
                  <SelectValue placeholder="Select language" />
                </SelectTrigger>
                <SelectContent>
                  {languages.map((lang) => (
                    <SelectItem key={lang.code} value={lang.code}>
                      {lang.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label className="text-foreground flex items-center gap-2">
                <Clock className="w-4 h-4" />
                Timezone
              </Label>
              <Select value={timezone} onValueChange={setTimezone}>
                <SelectTrigger className="glass-ultra border-border">
                  <SelectValue placeholder="Select timezone" />
                </SelectTrigger>
                <SelectContent>
                  {timezones.map((tz) => (
                    <SelectItem key={tz} value={tz}>
                      {tz}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Save Button */}
      <div className="flex justify-end">
        <Button 
          onClick={handleSave} 
          disabled={isLoading}
          className="glass-button bg-gradient-to-r from-[rgb(20,136,204)] to-[rgb(43,50,178)] hover:from-[rgb(20,136,204)]/90 hover:to-[rgb(43,50,178)]/90"
        >
          <Save className="w-4 h-4 mr-2" />
          {isLoading ? "Saving..." : "Save Changes"}
        </Button>
      </div>
    </div>
  )
}
