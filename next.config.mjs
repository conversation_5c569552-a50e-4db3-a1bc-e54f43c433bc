/** @type {import('next').NextConfig} */
const nextConfig = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    unoptimized: true,
  },
  webpack: (config, { isServer }) => {
    // Exclude MongoDB and other Node.js modules from client bundle
    if (!isServer) {
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      };

      // Exclude MongoDB and database-related modules from client bundle
      config.externals = config.externals || [];
      config.externals.push({
        mongodb: 'mongodb',
        'mongodb-client-encryption': 'mongodb-client-encryption',
        '@mongodb-js/zstd': '@mongodb-js/zstd',
        kerberos: 'kerberos',
        '@aws-sdk/credential-providers': '@aws-sdk/credential-providers',
        'gcp-metadata': 'gcp-metadata',
        'snappy': 'snappy',
        'socks': 'socks',
        'aws4': 'aws4',
        'bson-ext': 'bson-ext',
      });
    }

    return config;
  },
}

export default nextConfig
