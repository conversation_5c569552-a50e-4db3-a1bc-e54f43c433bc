/**
 * CythroDash - Referral Track Click API Route
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { NextRequest, NextResponse } from 'next/server';
import { ReferralsController } from '@/hooks/managers/controller/User/Referrals';
import { getClientIP } from '@/lib/security/config';
import { z } from 'zod';

// Input validation schema
const trackClickSchema = z.object({
  referral_code: z.string().min(1, 'Referral code is required'),
  session_id: z.string().optional(),
  device_info: z.object({
    browser: z.string().optional(),
    os: z.string().optional(),
    device_type: z.string().optional(),
    screen_resolution: z.string().optional(),
    language: z.string().optional(),
    timezone: z.string().optional()
  }).optional(),
  location_info: z.object({
    country: z.string().optional(),
    region: z.string().optional(),
    city: z.string().optional(),
    timezone: z.string().optional(),
    isp: z.string().optional()
  }).optional()
});

export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body = await request.json();
    const inputValidation = trackClickSchema.safeParse(body);

    if (!inputValidation.success) {
      return NextResponse.json(
        {
          success: false,
          message: 'Invalid input data',
          errors: inputValidation.error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message
          }))
        },
        { status: 400 }
      );
    }

    const { referral_code, session_id, device_info, location_info } = inputValidation.data;
    const ip = getClientIP(request);
    const userAgent = request.headers.get('user-agent') || 'unknown';

    // Track referral click using the controller
    const trackResponse = await ReferralsController.trackReferralClick({
      referral_code,
      ip_address: ip,
      user_agent: userAgent,
      session_id,
      device_info,
      location_info
    });

    if (!trackResponse.success) {
      return NextResponse.json(
        {
          success: false,
          message: trackResponse.message,
          errors: trackResponse.errors
        },
        { status: 400 }
      );
    }

    return NextResponse.json({
      success: true,
      message: trackResponse.message,
      data: trackResponse.data
    });

  } catch (error) {
    console.error('Referral track click API error:', error);
    
    return NextResponse.json(
      {
        success: false,
        message: 'An unexpected error occurred while tracking referral click',
        error: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}
