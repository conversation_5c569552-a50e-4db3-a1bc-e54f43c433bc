/**
 * CythroDash - Referrals Database Management
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { Collection, UpdateFilter } from 'mongodb';
import { connectToDatabase } from '../../../database/index';
import { 
  CythroDashReferral, 
  ReferralActionType,
  ReferralStatus,
  DeviceInfo,
  LocationInfo,
  ReferralStats,
  ReferralHelpers,
  REFERRALS_COLLECTION,
  REFERRALS_INDEXES
} from '../../../database/tables/cythro_dash_referrals';

// Create referral data interface
export interface CreateReferralData {
  referrer_user_id: number;
  referred_user_id?: number;
  referral_code: string;
  action_type: ReferralActionType;
  ip_address: string;
  device_info: DeviceInfo;
  location_info?: Partial<LocationInfo>;
  session_id?: string;
}

// Update referral data interface
export interface UpdateReferralData {
  status?: ReferralStatus;
  verified?: boolean;
  verified_at?: Date;
  verified_by?: number;
  claimed_at?: Date;
  claim_transaction_id?: string;
  is_suspicious?: boolean;
  fraud_score?: number;
  fraud_reasons?: string[];
  admin_notes?: string;
  reviewed_by?: number;
  reviewed_at?: Date;
}

// Database collection management
class CythroDashReferralsCollection {
  private collection!: Collection<CythroDashReferral>;
  private initialized = false;
  private nextId = 1;

  async getCollection(): Promise<Collection<CythroDashReferral>> {
    if (!this.initialized) {
      await this.initializeCollection();
    }
    return this.collection;
  }

  private async initializeCollection(): Promise<void> {
    const db = await connectToDatabase();
    this.collection = db.collection<CythroDashReferral>(REFERRALS_COLLECTION);
    
    // Create indexes for better performance
    await this.createIndexes();
    
    // Initialize auto-increment counter
    await this.initializeAutoIncrement();
    
    this.initialized = true;
  }

  private async createIndexes(): Promise<void> {
    try {
      for (const index of REFERRALS_INDEXES) {
        await this.collection.createIndex(
          index.key as any, 
          { 
            name: index.name,
            unique: index.unique || false,
            sparse: true
          }
        );
      }
      console.log('Referrals database indexes created successfully');
    } catch (error) {
      console.error('Error creating referrals database indexes:', error);
    }
  }

  private async initializeAutoIncrement(): Promise<void> {
    try {
      const lastReferral = await this.collection
        .findOne({}, { sort: { id: -1 } });
      
      this.nextId = lastReferral ? lastReferral.id + 1 : 1;
    } catch (error) {
      console.error('Error initializing auto-increment:', error);
      this.nextId = 1;
    }
  }

  async getNextId(): Promise<number> {
    return this.nextId++;
  }
}

// Singleton instance
const referralsCollection = new CythroDashReferralsCollection();

// Referral operations
export const referralOperations = {
  
  /**
   * Create a new referral record
   */
  async createReferral(data: CreateReferralData): Promise<CythroDashReferral> {
    try {
      const collection = await referralsCollection.getCollection();
      const id = await referralsCollection.getNextId();
      
      // Generate device fingerprint
      const fingerprint_hash = ReferralHelpers.generateDeviceFingerprint(
        data.device_info, 
        data.ip_address
      );
      
      // Get recent actions for fraud detection
      const recentActions = await this.getRecentActionsByIP(data.ip_address, 24); // Last 24 hours
      
      // Calculate fraud score
      const fraud_score = ReferralHelpers.calculateFraudScore(
        { ...data, fingerprint_hash, device_info: data.device_info },
        recentActions
      );
      
      // Determine if suspicious
      const is_suspicious = fraud_score > 50 || recentActions.length > 10;
      
      // Get reward amount
      const coins_earned = ReferralHelpers.getRewardAmount(data.action_type);
      
      const referral: CythroDashReferral = {
        id,
        referrer_user_id: data.referrer_user_id,
        referred_user_id: data.referred_user_id,
        referral_code: data.referral_code,
        action_type: data.action_type,
        status: ReferralStatus.PENDING,
        coins_earned,
        ip_address: data.ip_address,
        device_info: data.device_info,
        location_info: data.location_info,
        is_suspicious,
        fraud_score,
        verified: !is_suspicious && fraud_score < 30, // Auto-verify low-risk actions
        verified_at: !is_suspicious && fraud_score < 30 ? new Date() : undefined,
        session_id: data.session_id,
        fingerprint_hash,
        referrer_total_before: 0, // Will be updated by controller
        referrer_total_after: 0,  // Will be updated by controller
        created_at: new Date(),
        updated_at: new Date()
      };
      
      // Set fraud reasons if suspicious
      if (is_suspicious) {
        referral.fraud_reasons = [];
        if (recentActions.length > 10) {
          referral.fraud_reasons.push('Too many recent actions from same IP');
        }
        if (fraud_score > 70) {
          referral.fraud_reasons.push('High fraud score detected');
        }
      }
      
      const result = await collection.insertOne(referral);
      return { ...referral, _id: result.insertedId };
      
    } catch (error) {
      console.error('Error creating referral:', error);
      throw new Error('Failed to create referral record');
    }
  },

  /**
   * Get referral by ID
   */
  async getReferralById(id: number): Promise<CythroDashReferral | null> {
    try {
      const collection = await referralsCollection.getCollection();
      return await collection.findOne({ id });
    } catch (error) {
      console.error('Error getting referral by ID:', error);
      return null;
    }
  },

  /**
   * Get referrals by referrer user ID
   */
  async getReferralsByReferrer(
    referrer_user_id: number, 
    limit: number = 50, 
    offset: number = 0
  ): Promise<CythroDashReferral[]> {
    try {
      const collection = await referralsCollection.getCollection();
      return await collection
        .find({ referrer_user_id })
        .sort({ created_at: -1 })
        .skip(offset)
        .limit(limit)
        .toArray();
    } catch (error) {
      console.error('Error getting referrals by referrer:', error);
      return [];
    }
  },

  /**
   * Get recent actions by IP address
   */
  async getRecentActionsByIP(
    ip_address: string, 
    hours: number = 24
  ): Promise<CythroDashReferral[]> {
    try {
      const collection = await referralsCollection.getCollection();
      const since = new Date(Date.now() - (hours * 60 * 60 * 1000));
      
      return await collection
        .find({ 
          ip_address,
          created_at: { $gte: since }
        })
        .sort({ created_at: -1 })
        .toArray();
    } catch (error) {
      console.error('Error getting recent actions by IP:', error);
      return [];
    }
  },

  /**
   * Get recent actions by device fingerprint
   */
  async getRecentActionsByFingerprint(
    fingerprint_hash: string, 
    hours: number = 24
  ): Promise<CythroDashReferral[]> {
    try {
      const collection = await referralsCollection.getCollection();
      const since = new Date(Date.now() - (hours * 60 * 60 * 1000));
      
      return await collection
        .find({ 
          fingerprint_hash,
          created_at: { $gte: since }
        })
        .sort({ created_at: -1 })
        .toArray();
    } catch (error) {
      console.error('Error getting recent actions by fingerprint:', error);
      return [];
    }
  },

  /**
   * Update referral
   */
  async updateReferral(id: number, data: UpdateReferralData): Promise<CythroDashReferral | null> {
    try {
      const collection = await referralsCollection.getCollection();
      
      const updateData: UpdateFilter<CythroDashReferral> = {
        $set: {
          ...data,
          updated_at: new Date()
        }
      };
      
      const result = await collection.findOneAndUpdate(
        { id },
        updateData,
        { returnDocument: 'after' }
      );

      return result || null;
    } catch (error) {
      console.error('Error updating referral:', error);
      return null;
    }
  },

  /**
   * Get referral statistics for a user
   */
  async getReferralStats(referrer_user_id: number): Promise<ReferralStats> {
    try {
      const collection = await referralsCollection.getCollection();
      
      const pipeline = [
        { $match: { referrer_user_id } },
        {
          $group: {
            _id: null,
            total_clicks: {
              $sum: { $cond: [{ $eq: ['$action_type', ReferralActionType.CLICK] }, 1, 0] }
            },
            total_signups: {
              $sum: { $cond: [{ $eq: ['$action_type', ReferralActionType.SIGNUP] }, 1, 0] }
            },
            total_earnings: { $sum: '$coins_earned' },
            pending_earnings: {
              $sum: { $cond: [{ $eq: ['$status', ReferralStatus.PENDING] }, '$coins_earned', 0] }
            },
            claimed_earnings: {
              $sum: { $cond: [{ $eq: ['$status', ReferralStatus.CLAIMED] }, '$coins_earned', 0] }
            },
            last_activity: { $max: '$created_at' }
          }
        }
      ];
      
      const result = await collection.aggregate(pipeline).toArray();
      
      if (result.length === 0) {
        return {
          total_clicks: 0,
          total_signups: 0,
          total_earnings: 0,
          pending_earnings: 0,
          claimed_earnings: 0,
          conversion_rate: 0,
          average_earnings_per_referral: 0
        };
      }
      
      const stats = result[0];
      const conversion_rate = stats.total_clicks > 0 ? (stats.total_signups / stats.total_clicks) * 100 : 0;
      const total_referrals = stats.total_clicks + stats.total_signups;
      const average_earnings_per_referral = total_referrals > 0 ? stats.total_earnings / total_referrals : 0;
      
      return {
        total_clicks: stats.total_clicks,
        total_signups: stats.total_signups,
        total_earnings: stats.total_earnings,
        pending_earnings: stats.pending_earnings,
        claimed_earnings: stats.claimed_earnings,
        conversion_rate: Math.round(conversion_rate * 100) / 100,
        average_earnings_per_referral: Math.round(average_earnings_per_referral * 100) / 100,
        last_activity: stats.last_activity
      };
    } catch (error) {
      console.error('Error getting referral stats:', error);
      return {
        total_clicks: 0,
        total_signups: 0,
        total_earnings: 0,
        pending_earnings: 0,
        claimed_earnings: 0,
        conversion_rate: 0,
        average_earnings_per_referral: 0
      };
    }
  }
};
