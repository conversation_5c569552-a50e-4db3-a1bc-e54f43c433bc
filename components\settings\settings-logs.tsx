"use client"

import { useState, useEffect } from "react"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { 
  Search, 
  LogIn, 
  LogOut, 
  Key, 
  Shield, 
  Settings, 
  AlertTriangle,
  Clock,
  MapPin,
  Monitor,
  RefreshCw,
  Activity,
  Smartphone,
  Eye
} from 'lucide-react'
import { useAuthStore } from "@/stores/user-store"
import { useToast } from "@/hooks/use-toast"

// Security log interface matching our database schema
interface SecurityLog {
  id: number
  action: string
  severity: string
  status: string
  description: string
  details?: any
  ip_address?: string
  user_agent?: string
  country?: string
  city?: string
  device_type?: string
  browser?: string
  os?: string
  created_at: string
  is_suspicious: boolean
  requires_attention: boolean
}

interface SecurityStats {
  total_logs: number
  logs_by_severity: Record<string, number>
  logs_by_action: Record<string, number>
  suspicious_activities: number
  unresolved_issues: number
  recent_activity: number
}

export default function SettingsLogs() {
  const {
    currentUser,
    getSecurityLogs,
    getSecurityStats,
    clearSecurityLogsCache,
    securityLogs: cachedLogs,
    securityStats: cachedStats,
    isLoadingSecurityLogs,
    isLoadingSecurityStats
  } = useAuthStore()
  const { toast } = useToast()

  const [logs, setLogs] = useState<SecurityLog[]>(cachedLogs || [])
  const [filteredLogs, setFilteredLogs] = useState<SecurityLog[]>(cachedLogs || [])
  const [stats, setStats] = useState<SecurityStats | null>(cachedStats)
  const [searchTerm, setSearchTerm] = useState("")
  const [filterType, setFilterType] = useState("all")
  const [filterSeverity, setFilterSeverity] = useState("all")
  const [showDetails, setShowDetails] = useState<number | null>(null)

  // Sync with cached data when it changes
  useEffect(() => {
    if (cachedLogs && cachedLogs.length > 0) {
      setLogs(cachedLogs)
      setFilteredLogs(cachedLogs)
    }
  }, [cachedLogs])

  useEffect(() => {
    if (cachedStats) {
      setStats(cachedStats)
    }
  }, [cachedStats])

  // Load security logs and stats on component mount (only if not cached)
  useEffect(() => {
    if (currentUser) {
      // Only load if we don't have cached data
      if (!cachedLogs || cachedLogs.length === 0) {
        loadSecurityLogs()
      }
      if (!cachedStats) {
        loadSecurityStats()
      }
    }
  }, [currentUser, cachedLogs, cachedStats])

  const loadSecurityLogs = async (filters?: any, forceRefresh: boolean = false) => {
    try {
      const result = await getSecurityLogs({ limit: 100, ...filters }, forceRefresh)

      if (result.success && result.logs) {
        setLogs(result.logs)
        setFilteredLogs(result.logs)
      } else {
        setLogs([])
        setFilteredLogs([])
        if (result.message && !result.message.includes('cache')) {
          toast({
            title: "Info",
            description: result.message,
            variant: "default"
          })
        }
      }
    } catch (error) {
      console.error('Failed to load security logs:', error)
      setLogs([])
      setFilteredLogs([])
      toast({
        title: "Error",
        description: "Failed to load security logs",
        variant: "destructive"
      })
    }
  }

  const loadSecurityStats = async (forceRefresh: boolean = false) => {
    try {
      const result = await getSecurityStats(30, forceRefresh) // Last 30 days

      if (result.success && result.stats) {
        setStats(result.stats)
      }
    } catch (error) {
      console.error('Failed to load security stats:', error)
    }
  }

  // Filter logs based on search and filters
  useEffect(() => {
    let filtered = logs

    // Filter by search term
    if (searchTerm) {
      filtered = filtered.filter(log => 
        log.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (log.ip_address && log.ip_address.includes(searchTerm))
      )
    }

    // Filter by type
    if (filterType !== "all") {
      filtered = filtered.filter(log => log.action === filterType)
    }

    // Filter by severity
    if (filterSeverity !== "all") {
      filtered = filtered.filter(log => log.severity === filterSeverity)
    }

    setFilteredLogs(filtered)
  }, [logs, searchTerm, filterType, filterSeverity])

  const getSeverityColor = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'critical': return 'bg-red-500'
      case 'high': return 'bg-orange-500'
      case 'medium': return 'bg-yellow-500'
      case 'low': return 'bg-green-500'
      default: return 'bg-gray-500'
    }
  }

  const getActionIcon = (action: string) => {
    if (action.includes('login')) return <LogIn className="w-4 h-4" />
    if (action.includes('logout')) return <LogOut className="w-4 h-4" />
    if (action.includes('password')) return <Key className="w-4 h-4" />
    if (action.includes('profile')) return <Settings className="w-4 h-4" />
    return <Activity className="w-4 h-4" />
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString()
  }

  const getDeviceIcon = (deviceType?: string) => {
    switch (deviceType?.toLowerCase()) {
      case 'mobile': return <Smartphone className="w-4 h-4" />
      case 'tablet': return <Smartphone className="w-4 h-4" />
      default: return <Monitor className="w-4 h-4" />
    }
  }

  if (!currentUser) {
    return (
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardContent className="p-8 text-center">
          <AlertTriangle className="w-12 h-12 text-muted-foreground mx-auto mb-4" />
          <div className="text-muted-foreground">Please log in to view security logs.</div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Security Statistics */}
      {stats && (
        <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
          <CardHeader className="pb-4">
            <CardTitle className="text-foreground flex items-center gap-2">
              <Activity className="w-5 h-5" />
              Security Overview (Last 30 Days)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="text-center p-4 glass-ultra rounded-xl">
                <div className="text-2xl font-bold text-foreground">{stats.total_logs}</div>
                <div className="text-sm text-muted-foreground">Total Events</div>
              </div>
              <div className="text-center p-4 glass-ultra rounded-xl">
                <div className="text-2xl font-bold text-orange-500">{stats.suspicious_activities}</div>
                <div className="text-sm text-muted-foreground">Suspicious</div>
              </div>
              <div className="text-center p-4 glass-ultra rounded-xl">
                <div className="text-2xl font-bold text-red-500">{stats.unresolved_issues}</div>
                <div className="text-sm text-muted-foreground">Unresolved</div>
              </div>
              <div className="text-center p-4 glass-ultra rounded-xl">
                <div className="text-2xl font-bold text-blue-500">{stats.recent_activity}</div>
                <div className="text-sm text-muted-foreground">Last 24h</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Security Logs */}
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="text-foreground flex items-center gap-2">
              <Shield className="w-5 h-5" />
              Security Activity Log
            </CardTitle>
            <Button
              onClick={() => loadSecurityLogs(undefined, true)}
              disabled={isLoadingSecurityLogs}
              variant="outline"
              size="sm"
              className="glass-button"
            >
              <RefreshCw className={`w-4 h-4 mr-2 ${isLoadingSecurityLogs ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          {/* Search and Filters */}
          <div className="flex flex-col sm:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search logs..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 glass-ultra border-border"
              />
            </div>
            <Select value={filterType} onValueChange={setFilterType}>
              <SelectTrigger className="w-full sm:w-48 glass-ultra border-border">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="login_success">Login Success</SelectItem>
                <SelectItem value="login_failed">Login Failed</SelectItem>
                <SelectItem value="password_changed">Password Changed</SelectItem>
                <SelectItem value="profile_updated">Profile Updated</SelectItem>
              </SelectContent>
            </Select>
            <Select value={filterSeverity} onValueChange={setFilterSeverity}>
              <SelectTrigger className="w-full sm:w-48 glass-ultra border-border">
                <SelectValue placeholder="Filter by severity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Severities</SelectItem>
                <SelectItem value="low">Low</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="critical">Critical</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Logs List */}
          {isLoadingSecurityLogs ? (
            <div className="flex items-center justify-center py-12">
              <RefreshCw className="w-8 h-8 animate-spin text-muted-foreground" />
              <span className="ml-3 text-muted-foreground">Loading security logs...</span>
            </div>
          ) : filteredLogs.length > 0 ? (
            <div className="space-y-4">
              {filteredLogs.map((log) => (
                <div
                  key={log.id}
                  className={`p-4 glass-ultra rounded-xl border-l-4 ${
                    log.is_suspicious ? 'border-l-red-500' : 
                    log.requires_attention ? 'border-l-orange-500' : 
                    'border-l-green-500'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <div className="mt-1">
                        {getActionIcon(log.action)}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-medium text-foreground">{log.description}</span>
                          <Badge className={`${getSeverityColor(log.severity)} text-white text-xs`}>
                            {log.severity.toUpperCase()}
                          </Badge>
                          {log.is_suspicious && (
                            <Badge variant="destructive" className="text-xs">
                              <AlertTriangle className="w-3 h-3 mr-1" />
                              Suspicious
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center gap-4 text-sm text-muted-foreground">
                          <span className="flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {formatDate(log.created_at)}
                          </span>
                          {log.ip_address && (
                            <span className="flex items-center gap-1">
                              <MapPin className="w-3 h-3" />
                              {log.ip_address}
                              {log.city && ` (${log.city})`}
                            </span>
                          )}
                          {log.device_type && (
                            <span className="flex items-center gap-1">
                              {getDeviceIcon(log.device_type)}
                              {log.device_type}
                              {log.browser && ` - ${log.browser}`}
                            </span>
                          )}
                        </div>
                      </div>
                    </div>
                    {log.details && (
                      <Button
                        onClick={() => setShowDetails(showDetails === log.id ? null : log.id)}
                        variant="ghost"
                        size="sm"
                        className="ml-2"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                  
                  {showDetails === log.id && log.details && (
                    <div className="mt-3 p-3 bg-muted/20 rounded-lg">
                      <div className="text-sm text-muted-foreground">
                        <strong>Details:</strong>
                        <pre className="mt-1 text-xs overflow-x-auto">
                          {JSON.stringify(log.details, null, 2)}
                        </pre>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <Shield className="w-16 h-16 text-muted-foreground mx-auto mb-4" />
              <div className="text-lg font-medium text-muted-foreground mb-2">No security logs found</div>
              <div className="text-sm text-muted-foreground">
                {searchTerm || filterType !== "all" || filterSeverity !== "all" 
                  ? "Try adjusting your search or filters" 
                  : "Security events will appear here as they occur"
                }
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
