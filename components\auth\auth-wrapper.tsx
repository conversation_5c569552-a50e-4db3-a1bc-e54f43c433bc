"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { useAuthStore } from "@/stores/user-store"
import { Loader2 } from "lucide-react"

interface AuthWrapperProps {
  children: React.ReactNode
  requireAuth?: boolean
  requireAdmin?: boolean
  redirectTo?: string
}

export default function AuthWrapper({ 
  children, 
  requireAuth = true, 
  requireAdmin = false,
  redirectTo = "/auth/login" 
}: AuthWrapperProps) {
  const router = useRouter()
  const { isAuthenticated, currentUser, checkSession, isLoading } = useAuthStore()
  const [isChecking, setIsChecking] = useState(true)

  useEffect(() => {
    const validateAuth = async () => {
      console.log('AuthWrapper validateAuth called:', { requireAuth, isAuthenticated, hasUser: !!currentUser })

      if (!requireAuth) {
        setIsChecking(false)
        return
      }

      try {
        // Check if we have authentication data in the store
        if (isAuthenticated && currentUser) {
          console.log('User already authenticated:', currentUser.username)
          // Check admin requirement
          if (requireAdmin && currentUser.role !== 0) {
            router.push("/") // Redirect to dashboard if not admin
            return
          }
          setIsChecking(false)
          return
        }

        // Only check session if we don't have any auth data
        if (!isAuthenticated && !currentUser) {
          console.log('No auth data, checking session...')
          const sessionValid = await checkSession()
          if (!sessionValid) {
            console.log('Session invalid, redirecting to login')
            router.push(redirectTo)
            return
          }

          // After successful session check, verify admin requirement again
          const updatedUser = useAuthStore.getState().currentUser
          if (requireAdmin && updatedUser?.role !== 0) {
            router.push("/") // Redirect to dashboard if not admin
            return
          }
        }

        setIsChecking(false)
      } catch (error) {
        console.error('Auth validation error:', error)
        router.push(redirectTo)
      }
    }

    validateAuth()
  }, [isAuthenticated, currentUser, requireAuth, requireAdmin, checkSession, router, redirectTo])

  // Show loading spinner while checking authentication
  if (isChecking || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-background via-background/95 to-background/90">
        <div className="flex flex-col items-center gap-4">
          <Loader2 className="h-8 w-8 animate-spin text-brand" />
          <p className="text-muted-foreground">Checking authentication...</p>
        </div>
      </div>
    )
  }

  // If auth is required but user is not authenticated, don't render children
  if (requireAuth && !isAuthenticated) {
    return null
  }

  // If admin is required but user is not admin, don't render children
  if (requireAdmin && currentUser?.role !== 0) {
    return null
  }

  return <>{children}</>
}
