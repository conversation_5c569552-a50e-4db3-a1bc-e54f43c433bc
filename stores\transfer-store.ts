"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"
import { useAuthStore } from "@/stores/user-store"
import { TransfersControllerClientSafe as TransfersController } from "@/hooks/managers/controller/User/TransfersClientSafe"

// Transfer data interfaces
export interface TransferStats {
  total_sent: number
  total_received: number
  transfer_count: number
  daily_sent: number
  daily_received: number
  daily_count: number
}

export interface TransferHistoryItem {
  id: number
  amount: number
  type: string
  status: string
  note?: string
  reference_id?: string
  created_at: string
  processed_at?: string
  is_sender: boolean
  other_user: {
    id: number
    username: string
  }
}

export interface TransferData {
  stats: TransferStats
  history: TransferHistoryItem[]
  total_history: number
  has_more: boolean
}

export interface SendTransferRequest {
  receiver_user_id: number
  amount: number
  note?: string
}

export interface TransferValidation {
  valid: boolean
  message?: string
  errors?: string[]
}

// Transfer store interface
interface TransferStore {
  // State
  transferData: TransferData | null
  isLoading: boolean
  error: string | null
  lastFetch: Date | null

  // Actions
  fetchTransferStats: () => Promise<{ success: boolean; message?: string }>
  fetchTransferHistory: (limit?: number, offset?: number) => Promise<{ success: boolean; message?: string }>
  sendTransfer: (request: SendTransferRequest) => Promise<{ success: boolean; message?: string; data?: any }>
  validateTransfer: (request: SendTransferRequest) => Promise<TransferValidation>
  clearError: () => void
  reset: () => void
}

export const useTransferStore = create<TransferStore>()(
  persist(
    (set, get) => ({
      // Initial state
      transferData: null,
      isLoading: false,
      error: null,
      lastFetch: null,

      // Fetch transfer statistics
      fetchTransferStats: async () => {
        set({ isLoading: true, error: null })

        try {
          console.log('📊 Fetching transfer stats directly from controller...')

          // Get current user from auth store
          const authStore = useAuthStore.getState()
          const currentUser = authStore.currentUser

          if (!currentUser) {
            console.log('❌ No authenticated user found')
            set({
              error: 'User not authenticated',
              isLoading: false
            })
            return { success: false, message: 'User not authenticated' }
          }

          // Call controller directly
          const result = await TransfersController.getTransferStats({
            user_id: currentUser.id,
            days: 30
          })

          if (result.success && result.data) {
            console.log('✅ Transfer stats fetched successfully')

            // Update state with stats
            set((state) => ({
              transferData: {
                ...state.transferData,
                stats: result.data
              } as TransferData,
              isLoading: false,
              lastFetch: new Date()
            }))

            return { success: true }
          } else {
            console.log('❌ Failed to fetch transfer stats:', result.message)
            set({
              error: result.message || 'Failed to fetch transfer statistics',
              isLoading: false
            })
            return { success: false, message: result.message }
          }
        } catch (error) {
          console.error('💥 Controller transfer stats error:', error)
          set({
            error: 'Error occurred while fetching transfer statistics',
            isLoading: false
          })
          return { success: false, message: 'Error occurred while fetching transfer statistics' }
        }
      },

      // Fetch transfer history
      fetchTransferHistory: async (limit = 50, offset = 0) => {
        set({ isLoading: true, error: null })

        try {
          console.log('📋 Fetching transfer history directly from controller...')

          // Get current user from auth store
          const authStore = useAuthStore.getState()
          const currentUser = authStore.currentUser

          if (!currentUser) {
            console.log('❌ No authenticated user found')
            set({
              error: 'User not authenticated',
              isLoading: false
            })
            return { success: false, message: 'User not authenticated' }
          }

          // Call controller directly
          const result = await TransfersController.getTransferHistory({
            user_id: currentUser.id,
            limit,
            offset
          })

          if (result.success && result.data) {
            console.log('✅ Transfer history fetched successfully')

            // Update state with history
            set((state) => ({
              transferData: {
                stats: state.transferData?.stats || {
                  total_sent: 0,
                  total_received: 0,
                  transfer_count: 0,
                  daily_sent: 0,
                  daily_received: 0,
                  daily_count: 0
                },
                history: result.data.transfers || [],
                total_history: result.data.total || 0,
                has_more: result.data.has_more || false
              },
              isLoading: false,
              lastFetch: new Date()
            }))

            return { success: true }
          } else {
            console.log('❌ Failed to fetch transfer history:', result.message)
            set({
              error: result.message || 'Failed to fetch transfer history',
              isLoading: false
            })
            return { success: false, message: result.message }
          }
        } catch (error) {
          console.error('💥 Controller transfer history error:', error)
          set({
            error: 'Error occurred while fetching transfer history',
            isLoading: false
          })
          return { success: false, message: 'Error occurred while fetching transfer history' }
        }
      },

      // Send transfer
      sendTransfer: async (request: SendTransferRequest) => {
        set({ isLoading: true, error: null })

        try {
          console.log('💸 Sending transfer directly from controller...', { receiver: request.receiver_user_id, amount: request.amount })

          // Get current user from auth store
          const authStore = useAuthStore.getState()
          const currentUser = authStore.currentUser

          if (!currentUser) {
            console.log('❌ No authenticated user found')
            set({
              error: 'User not authenticated',
              isLoading: false
            })
            return { success: false, message: 'User not authenticated' }
          }

          // Prepare device info
          const deviceInfo = {
            screen_resolution: typeof window !== 'undefined' ? `${window.screen.width}x${window.screen.height}` : undefined,
            timezone: typeof window !== 'undefined' ? Intl.DateTimeFormat().resolvedOptions().timeZone : undefined,
            language: typeof window !== 'undefined' ? navigator.language : undefined,
            user_agent: typeof window !== 'undefined' ? navigator.userAgent : undefined,
          }

          // Call controller directly
          const result = await TransfersController.sendTransfer({
            sender_user_id: currentUser.id,
            receiver_user_id: request.receiver_user_id,
            amount: request.amount,
            note: request.note,
            ip_address: 'unknown', // Will be handled by controller
            user_agent: deviceInfo.user_agent || 'unknown',
            device_info: deviceInfo
          })

          if (result.success) {
            console.log('✅ Transfer sent successfully:', result.data)

            // Immediately update user's coin balance in auth store using transfer-specific function
            const transferAmount = request.amount
            console.log('💰 Updating user coins immediately after transfer:', -transferAmount)
            authStore.syncCoinsFromTransfer(transferAmount, false) // false = sent (not received)

            // Refresh transfer data
            await get().fetchTransferStats()
            await get().fetchTransferHistory()

            set({ isLoading: false })

            return {
              success: true,
              message: result.message,
              data: result.data
            }
          } else {
            console.log('❌ Transfer failed:', result.message)
            set({
              error: result.message || 'Transfer failed',
              isLoading: false
            })
            return { success: false, message: result.message }
          }
        } catch (error) {
          console.error('💥 Controller transfer error:', error)
          set({
            error: 'Error occurred while sending transfer',
            isLoading: false
          })
          return { success: false, message: 'Error occurred while sending transfer' }
        }
      },

      // Validate transfer
      validateTransfer: async (request: SendTransferRequest): Promise<TransferValidation> => {
        try {
          console.log('🔍 Validating transfer directly from controller...', { receiver: request.receiver_user_id, amount: request.amount })

          // Get current user from auth store
          const authStore = useAuthStore.getState()
          const currentUser = authStore.currentUser

          if (!currentUser) {
            console.log('❌ No authenticated user found')
            return {
              valid: false,
              message: 'User not authenticated'
            }
          }

          // Call controller directly
          const result = await TransfersController.validateTransfer({
            sender_user_id: currentUser.id,
            receiver_user_id: request.receiver_user_id,
            amount: request.amount
          })

          if (result.valid) {
            console.log('✅ Transfer validation result:', result)
            return result
          } else {
            console.log('❌ Transfer validation failed:', result.message)
            return {
              valid: false,
              message: result.message || 'Validation failed'
            }
          }
        } catch (error) {
          console.error('💥 Controller validation error:', error)
          return {
            valid: false,
            message: 'Error during validation'
          }
        }
      },

      // Clear error
      clearError: () => {
        set({ error: null })
      },

      // Reset store
      reset: () => {
        set({
          transferData: null,
          isLoading: false,
          error: null,
          lastFetch: null
        })
      }
    }),
    {
      name: 'transfer-store',
      partialize: (state) => ({
        transferData: state.transferData,
        lastFetch: state.lastFetch
      })
    }
  )
)
