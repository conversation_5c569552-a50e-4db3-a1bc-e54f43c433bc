/**
 * CythroDash - Referrals Controller
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { userOperations } from '../../database/user';
import { referralOperations, CreateReferralData } from '../../database/referrals';
import { 
  ReferralActionType, 
  ReferralStatus, 
  DeviceInfo, 
  LocationInfo,
  ReferralHelpers 
} from '../../../../database/tables/cythro_dash_referrals';
import { SecurityLogsController } from '../Security/Logs';
import { SecurityLogAction } from '../../../../database/tables/cythro_dash_users_logs';

// Request interfaces
export interface ReferralClickRequest {
  referral_code: string;
  ip_address: string;
  user_agent: string;
  session_id?: string;
  device_info?: Partial<DeviceInfo>;
  location_info?: Partial<LocationInfo>;
}

export interface ReferralSignupRequest {
  referral_code: string;
  referred_user_id: number;
  ip_address: string;
  user_agent: string;
  session_id?: string;
  device_info?: Partial<DeviceInfo>;
  location_info?: Partial<LocationInfo>;
}

export interface ClaimReferralEarningsRequest {
  user_id: number;
  ip_address?: string;
  user_agent?: string;
}

export interface GetReferralStatsRequest {
  user_id: number;
}

// Response interfaces
export interface ReferralResponse {
  success: boolean;
  message: string;
  data?: any;
  errors?: Array<{
    field: string;
    message: string;
  }>;
}

export interface ReferralStatsResponse extends ReferralResponse {
  data?: {
    stats: {
      total_clicks: number;
      total_signups: number;
      total_earnings: number;
      pending_earnings: number;
      claimed_earnings: number;
      conversion_rate: number;
      average_earnings_per_referral: number;
      last_activity?: Date;
    };
    referral_code: string;
    recent_activities: Array<{
      id: number;
      action_type: ReferralActionType;
      coins_earned: number;
      status: ReferralStatus;
      created_at: Date;
      is_suspicious: boolean;
    }>;
  };
}

// Referrals controller class
export class ReferralsController {



  /**
   * Check if referral system is enabled
   */
  private static isReferralSystemEnabled(): boolean {
    const envValue = process.env.NEXT_PUBLIC_REFERRAL_PROGRAM;
    // Enable by default if not explicitly disabled
    const enabled = envValue !== 'false';
    console.log('🔧 Referral system enabled:', enabled, '(env value:', envValue, ')');
    return enabled;
  }

  /**
   * Parse device information from user agent
   */
  private static parseDeviceInfo(userAgent: string, additionalInfo?: Partial<DeviceInfo>): DeviceInfo {
    const deviceInfo: DeviceInfo = {
      user_agent: userAgent,
      ...additionalInfo
    };

    // Basic browser detection
    if (userAgent.includes('Chrome')) deviceInfo.browser = 'Chrome';
    else if (userAgent.includes('Firefox')) deviceInfo.browser = 'Firefox';
    else if (userAgent.includes('Safari')) deviceInfo.browser = 'Safari';
    else if (userAgent.includes('Edge')) deviceInfo.browser = 'Edge';

    // Basic OS detection
    if (userAgent.includes('Windows')) deviceInfo.os = 'Windows';
    else if (userAgent.includes('Mac')) deviceInfo.os = 'macOS';
    else if (userAgent.includes('Linux')) deviceInfo.os = 'Linux';
    else if (userAgent.includes('Android')) deviceInfo.os = 'Android';
    else if (userAgent.includes('iOS')) deviceInfo.os = 'iOS';

    // Basic device type detection
    if (userAgent.includes('Mobile')) deviceInfo.device_type = 'mobile';
    else if (userAgent.includes('Tablet')) deviceInfo.device_type = 'tablet';
    else deviceInfo.device_type = 'desktop';

    return deviceInfo;
  }

  /**
   * Validate referral code and get referrer
   */
  private static async validateReferralCode(referralCode: string): Promise<{ valid: boolean; referrer?: any; message?: string }> {
    console.log('🔍 Checking referral code format:', {
      code: referralCode,
      length: referralCode.length,
      pattern: /^[A-Z0-9]{10,25}$/.test(referralCode)
    });

    if (!ReferralHelpers.isValidReferralCode(referralCode)) {
      console.log('❌ Referral code format validation failed');
      return { valid: false, message: 'Invalid referral code format' };
    }
    console.log('✅ Referral code format is valid');

    const referrer = await userOperations.getUserByReferralCode(referralCode);
    if (!referrer) {
      return { valid: false, message: 'Referral code not found' };
    }

    if (referrer.banned || referrer.deleted) {
      return { valid: false, message: 'Referral code is no longer valid' };
    }

    return { valid: true, referrer };
  }

  /**
   * Check for fraud and rate limiting
   */
  private static async checkFraudAndRateLimit(
    ipAddress: string, 
    actionType: ReferralActionType
  ): Promise<{ allowed: boolean; reason?: string }> {
    const recentActions = await referralOperations.getRecentActionsByIP(ipAddress, 24);
    
    // Check rate limits based on action type
    if (actionType === ReferralActionType.CLICK) {
      const recentClicks = recentActions.filter(a => a.action_type === ReferralActionType.CLICK);
      if (recentClicks.length >= 10) { // Max 10 clicks per day per IP
        return { allowed: false, reason: 'Too many clicks from this IP address today' };
      }
    } else if (actionType === ReferralActionType.SIGNUP) {
      const recentSignups = recentActions.filter(a => a.action_type === ReferralActionType.SIGNUP);
      if (recentSignups.length >= 2) { // Max 2 signups per day per IP
        return { allowed: false, reason: 'Too many signups from this IP address today' };
      }
    }

    // Check for rapid actions (less than 30 seconds apart)
    if (recentActions.length > 0) {
      const lastAction = recentActions[0];
      const timeDiff = Date.now() - lastAction.created_at.getTime();
      if (timeDiff < 30000) { // 30 seconds
        return { allowed: false, reason: 'Please wait before performing another action' };
      }
    }

    return { allowed: true };
  }

  /**
   * Handle referral click (visiting page via referral link)
   */
  static async handleReferralClick(request: ReferralClickRequest): Promise<ReferralResponse> {
    try {
      console.log('🔗 Processing referral click:', {
        referral_code: request.referral_code,
        ip_address: request.ip_address,
        user_agent: request.user_agent?.substring(0, 50) + '...'
      });

      // Check if referral system is enabled
      if (!this.isReferralSystemEnabled()) {
        console.log('❌ Referral system is disabled');
        return {
          success: false,
          message: 'Referral system is currently disabled'
        };
      }

      // Validate referral code
      console.log('🔍 Validating referral code:', request.referral_code);
      const validation = await this.validateReferralCode(request.referral_code);
      if (!validation.valid) {
        console.log('❌ Referral code validation failed:', validation.message);
        return {
          success: false,
          message: validation.message || 'Invalid referral code'
        };
      }
      console.log('✅ Referral code validated for user:', validation.referrer?.username);

      // Check fraud and rate limiting
      const fraudCheck = await this.checkFraudAndRateLimit(request.ip_address, ReferralActionType.CLICK);
      if (!fraudCheck.allowed) {
        return {
          success: false,
          message: fraudCheck.reason || 'Action not allowed'
        };
      }

      // Parse device information
      const deviceInfo = this.parseDeviceInfo(request.user_agent, request.device_info);

      // Create referral record
      const referralData: CreateReferralData = {
        referrer_user_id: validation.referrer!.id,
        referral_code: request.referral_code,
        action_type: ReferralActionType.CLICK,
        ip_address: request.ip_address,
        device_info: deviceInfo,
        location_info: request.location_info,
        session_id: request.session_id
      };

      console.log('💾 Creating referral click record...');
      const referral = await referralOperations.createReferral(referralData);
      console.log('✅ Referral click record created:', {
        id: referral.id,
        is_suspicious: referral.is_suspicious,
        verified: referral.verified
      });

      // Update referrer's total if not suspicious
      if (!referral.is_suspicious && referral.verified) {
        console.log('💰 Updating referrer earnings (+15 coins)');
        await userOperations.updateUser(validation.referrer!.id, {
          referral_earnings: validation.referrer!.referral_earnings + 15
        });

        // Log the referral click
        await SecurityLogsController.createLog({
          user_id: validation.referrer!.id,
          action: SecurityLogAction.REFERRAL_CLICK,
          description: `Referral click earned 15 coins`,
          details: {
            referral_id: referral.id,
            coins_earned: 15,
            ip_address: request.ip_address
          },
          ip_address: request.ip_address
        });
        console.log('📝 Security log created for referral click');
      } else {
        console.log('⚠️ Referral click flagged as suspicious or unverified');
      }

      return {
        success: true,
        message: referral.is_suspicious ? 
          'Referral click recorded but flagged for review' : 
          'Referral click recorded successfully',
        data: {
          referral_id: referral.id,
          coins_earned: referral.is_suspicious ? 0 : 15,
          is_suspicious: referral.is_suspicious,
          fraud_score: referral.fraud_score
        }
      };

    } catch (error) {
      console.error('Error handling referral click:', error);
      return {
        success: false,
        message: 'Failed to process referral click'
      };
    }
  }

  /**
   * Handle referral signup (user registered via referral)
   */
  static async handleReferralSignup(request: ReferralSignupRequest): Promise<ReferralResponse> {
    try {
      console.log('👤 Processing referral signup:', {
        referral_code: request.referral_code,
        referred_user_id: request.referred_user_id,
        ip_address: request.ip_address
      });

      // Check if referral system is enabled
      if (!this.isReferralSystemEnabled()) {
        console.log('❌ Referral system is disabled for signup');
        return {
          success: false,
          message: 'Referral system is currently disabled'
        };
      }

      // Validate referral code
      console.log('🔍 Validating referral code for signup:', request.referral_code);
      const validation = await this.validateReferralCode(request.referral_code);
      if (!validation.valid) {
        console.log('❌ Referral code validation failed for signup:', validation.message);
        return {
          success: false,
          message: validation.message || 'Invalid referral code'
        };
      }
      console.log('✅ Referral code validated for signup, referrer:', validation.referrer?.username);

      // Check if referred user exists
      console.log('👤 Checking referred user exists:', request.referred_user_id);
      const referredUser = await userOperations.getUserById(request.referred_user_id);
      if (!referredUser) {
        console.log('❌ Referred user not found:', request.referred_user_id);
        return {
          success: false,
          message: 'Referred user not found'
        };
      }
      console.log('✅ Referred user found:', referredUser.username);

      // Check if user is referring themselves
      if (validation.referrer!.id === request.referred_user_id) {
        console.log('❌ User trying to refer themselves');
        return {
          success: false,
          message: 'Cannot refer yourself'
        };
      }

      // Check fraud and rate limiting
      const fraudCheck = await this.checkFraudAndRateLimit(request.ip_address, ReferralActionType.SIGNUP);
      if (!fraudCheck.allowed) {
        return {
          success: false,
          message: fraudCheck.reason || 'Action not allowed'
        };
      }

      // Parse device information
      const deviceInfo = this.parseDeviceInfo(request.user_agent, request.device_info);

      // Create referral record
      const referralData: CreateReferralData = {
        referrer_user_id: validation.referrer!.id,
        referred_user_id: request.referred_user_id,
        referral_code: request.referral_code,
        action_type: ReferralActionType.SIGNUP,
        ip_address: request.ip_address,
        device_info: deviceInfo,
        location_info: request.location_info,
        session_id: request.session_id
      };

      console.log('💾 Creating referral signup record...');
      const referral = await referralOperations.createReferral(referralData);
      console.log('✅ Referral signup record created:', {
        id: referral.id,
        is_suspicious: referral.is_suspicious,
        verified: referral.verified
      });

      // Update referrer's total if not suspicious
      if (!referral.is_suspicious && referral.verified) {
        console.log('💰 Updating referrer earnings (+30 coins for signup)');
        await userOperations.updateUser(validation.referrer!.id, {
          referral_earnings: validation.referrer!.referral_earnings + 30
        });

        // Log the referral signup
        await SecurityLogsController.createLog({
          user_id: validation.referrer!.id,
          action: SecurityLogAction.REFERRAL_SIGNUP,
          description: `Referral signup earned 30 coins`,
          details: {
            referral_id: referral.id,
            referred_user_id: request.referred_user_id,
            coins_earned: 30,
            ip_address: request.ip_address
          },
          ip_address: request.ip_address
        });
        console.log('📝 Security log created for referral signup');
      } else {
        console.log('⚠️ Referral signup flagged as suspicious or unverified');
      }

      return {
        success: true,
        message: referral.is_suspicious ? 
          'Referral signup recorded but flagged for review' : 
          'Referral signup recorded successfully',
        data: {
          referral_id: referral.id,
          coins_earned: referral.is_suspicious ? 0 : 30,
          is_suspicious: referral.is_suspicious,
          fraud_score: referral.fraud_score
        }
      };

    } catch (error) {
      console.error('Error handling referral signup:', error);
      return {
        success: false,
        message: 'Failed to process referral signup'
      };
    }
  }

  /**
   * Claim pending referral earnings
   */
  static async claimReferralEarnings(request: ClaimReferralEarningsRequest): Promise<ReferralResponse> {
    try {
      // Check if referral system is enabled
      if (!this.isReferralSystemEnabled()) {
        return {
          success: false,
          message: 'Referral system is currently disabled'
        };
      }

      // Get user
      const user = await userOperations.getUserById(request.user_id);
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      if (user.banned || user.deleted) {
        return {
          success: false,
          message: 'Account is not eligible for claiming rewards'
        };
      }

      // Get user's referral stats
      const stats = await referralOperations.getReferralStats(request.user_id);

      if (stats.pending_earnings <= 0) {
        return {
          success: false,
          message: 'No pending earnings to claim'
        };
      }

      // Get all pending referrals for this user
      const pendingReferrals = await referralOperations.getReferralsByReferrer(request.user_id);
      const eligibleReferrals = pendingReferrals.filter(r =>
        r.status === ReferralStatus.PENDING &&
        ReferralHelpers.isEligibleForReward(r)
      );

      if (eligibleReferrals.length === 0) {
        return {
          success: false,
          message: 'No eligible earnings to claim'
        };
      }

      // Calculate total claimable amount
      const claimableAmount = eligibleReferrals.reduce((total, r) => total + r.coins_earned, 0);

      // Generate claim transaction ID
      const claimTransactionId = `claim_${request.user_id}_${Date.now()}`;

      // Update all eligible referrals to claimed status
      const claimDate = new Date();
      for (const referral of eligibleReferrals) {
        await referralOperations.updateReferral(referral.id, {
          status: ReferralStatus.CLAIMED,
          claimed_at: claimDate,
          claim_transaction_id: claimTransactionId
        });
      }

      // Update user's coin balance
      const newCoinBalance = user.coins + claimableAmount;
      await userOperations.updateUser(request.user_id, {
        coins: newCoinBalance,
        total_coins_earned: user.total_coins_earned + claimableAmount
      });

      // Log the claim action
      await SecurityLogsController.createLog({
        user_id: request.user_id,
        action: SecurityLogAction.REFERRAL_CLAIM,
        description: `Claimed ${claimableAmount} coins from referral earnings`,
        details: {
          transaction_id: claimTransactionId,
          coins_claimed: claimableAmount,
          referrals_claimed: eligibleReferrals.length,
          new_balance: newCoinBalance
        },
        ip_address: request.ip_address
      });

      return {
        success: true,
        message: `Successfully claimed ${claimableAmount} coins`,
        data: {
          transaction_id: claimTransactionId,
          coins_claimed: claimableAmount,
          referrals_claimed: eligibleReferrals.length,
          new_balance: newCoinBalance
        }
      };

    } catch (error) {
      console.error('Error claiming referral earnings:', error);
      return {
        success: false,
        message: 'Failed to claim referral earnings'
      };
    }
  }

  /**
   * Get referral statistics and recent activity
   */
  static async getReferralStats(request: GetReferralStatsRequest): Promise<ReferralStatsResponse> {
    try {
      // Get user
      const user = await userOperations.getUserById(request.user_id);
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Get referral statistics
      const stats = await referralOperations.getReferralStats(request.user_id);

      // Get recent referral activities (last 20)
      const recentReferrals = await referralOperations.getReferralsByReferrer(request.user_id, 20, 0);
      const recentActivities = recentReferrals.map(r => ({
        id: r.id,
        action_type: r.action_type,
        coins_earned: r.coins_earned,
        status: r.status,
        created_at: r.created_at,
        is_suspicious: r.is_suspicious
      }));

      return {
        success: true,
        message: 'Referral statistics retrieved successfully',
        data: {
          stats,
          referral_code: user.referral_code || 'Not generated',
          recent_activities: recentActivities
        }
      };

    } catch (error) {
      console.error('Error getting referral stats:', error);
      return {
        success: false,
        message: 'Failed to retrieve referral statistics'
      };
    }
  }

  /**
   * Generate or regenerate referral code for user
   */
  static async generateReferralCode(userId: number): Promise<ReferralResponse> {
    try {
      const user = await userOperations.getUserById(userId);
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Generate new referral code if user doesn't have one
      if (!user.referral_code) {
        const referralCode = this.generateUniqueReferralCode(user.username);

        await userOperations.updateUser(userId, {
          referral_code: referralCode
        });

        return {
          success: true,
          message: 'Referral code generated successfully',
          data: { referral_code: referralCode }
        };
      }

      return {
        success: true,
        message: 'User already has a referral code',
        data: { referral_code: user.referral_code }
      };

    } catch (error) {
      console.error('Error generating referral code:', error);
      return {
        success: false,
        message: 'Failed to generate referral code'
      };
    }
  }

  /**
   * Generate a unique referral code
   */
  private static generateUniqueReferralCode(username: string): string {
    const timestamp = Date.now().toString(36);
    const randomStr = Math.random().toString(36).substring(2, 8);
    return `${username.substring(0, 4).toUpperCase()}${timestamp}${randomStr}`.toUpperCase();
  }
};
