"use client"

import { Send, Download, Clock, CheckCircle, XCircle, <PERSON><PERSON><PERSON>riangle, History } from "lucide-react"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { TransferHistoryItem } from "@/stores/transfer-store"

interface TransferHistoryProps {
  history: TransferHistoryItem[]
  hasMore: boolean
}

export default function TransferHistory({ history, hasMore }: TransferHistoryProps) {
  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-emerald-400" />
      case 'pending':
        return <Clock className="w-4 h-4 text-yellow-400" />
      case 'failed':
        return <XCircle className="w-4 h-4 text-red-400" />
      case 'cancelled':
        return <XCircle className="w-4 h-4 text-gray-400" />
      default:
        return <AlertTriangle className="w-4 h-4 text-orange-400" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-emerald-400/10 text-emerald-400 border-emerald-400/20'
      case 'pending':
        return 'bg-yellow-400/10 text-yellow-400 border-yellow-400/20'
      case 'failed':
        return 'bg-red-400/10 text-red-400 border-red-400/20'
      case 'cancelled':
        return 'bg-gray-400/10 text-gray-400 border-gray-400/20'
      default:
        return 'bg-orange-400/10 text-orange-400 border-orange-400/20'
    }
  }

  const formatDate = (dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60)

    if (diffInHours < 1) {
      const diffInMinutes = Math.floor(diffInHours * 60)
      return `${diffInMinutes} minute${diffInMinutes !== 1 ? 's' : ''} ago`
    } else if (diffInHours < 24) {
      const hours = Math.floor(diffInHours)
      return `${hours} hour${hours !== 1 ? 's' : ''} ago`
    } else {
      return date.toLocaleDateString()
    }
  }

  if (history.length === 0) {
    return (
      <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
        <CardHeader>
          <CardTitle className="flex items-center gap-3">
            <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
              <History className="w-5 h-5 text-white" />
            </div>
            Transfer History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <History className="w-16 h-16 text-muted-foreground mx-auto mb-4 opacity-50" />
            <p className="text-muted-foreground">No transfers yet</p>
            <p className="text-sm text-muted-foreground mt-2">Your transfer history will appear here</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="glass-ultra border border-border rounded-2xl shadow-glass">
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <div className="w-8 h-8 rounded-xl bg-gradient-to-br from-purple-500 to-pink-600 flex items-center justify-center">
            <History className="w-5 h-5 text-white" />
          </div>
          Transfer History
          <Badge className="bg-purple-400/10 text-purple-400 border-purple-400/20">
            {history.length} transfers
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {history.map((transfer) => (
            <div
              key={transfer.id}
              className="flex items-center justify-between p-4 glass-ultra rounded-xl border border-border/50 hover:border-border/80 transition-colors"
            >
              <div className="flex items-center gap-4">
                {/* Transfer Direction Icon */}
                <div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
                  transfer.is_sender 
                    ? 'bg-gradient-to-br from-red-500 to-pink-600' 
                    : 'bg-gradient-to-br from-emerald-500 to-green-600'
                }`}>
                  {transfer.is_sender ? (
                    <Send className="w-5 h-5 text-white" />
                  ) : (
                    <Download className="w-5 h-5 text-white" />
                  )}
                </div>

                {/* Transfer Details */}
                <div>
                  <div className="flex items-center gap-2 mb-1">
                    <p className="font-semibold text-foreground">
                      {transfer.is_sender ? 'Sent to' : 'Received from'} {transfer.other_user.username}
                    </p>
                    {getStatusIcon(transfer.status)}
                  </div>
                  <div className="flex items-center gap-3 text-sm text-muted-foreground">
                    <span>{formatDate(transfer.created_at)}</span>
                    {transfer.reference_id && (
                      <>
                        <span>•</span>
                        <span className="font-mono text-xs">{transfer.reference_id}</span>
                      </>
                    )}
                  </div>
                  {transfer.note && (
                    <p className="text-sm text-muted-foreground mt-1 italic">"{transfer.note}"</p>
                  )}
                </div>
              </div>

              {/* Amount and Status */}
              <div className="text-right">
                <div className="flex items-center gap-1 mb-2">
                  <span className={`text-lg font-bold ${
                    transfer.is_sender ? 'text-red-500' : 'text-emerald-500'
                  }`}>
                    {transfer.is_sender ? '-' : '+'}
                    {transfer.amount.toLocaleString()}
                  </span>
                  <span className="text-sm text-muted-foreground">coins</span>
                </div>
                <Badge className={getStatusColor(transfer.status)}>
                  {transfer.status}
                </Badge>
              </div>
            </div>
          ))}

          {/* Load More Button */}
          {hasMore && (
            <div className="text-center pt-4">
              <Button 
                variant="outline" 
                className="secondary-button bg-transparent"
                onClick={() => {
                  // TODO: Implement load more functionality
                  console.log('Load more transfers')
                }}
              >
                Load More Transfers
              </Button>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
