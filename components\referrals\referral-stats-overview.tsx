"use client"

import { TrendingUp, Users, Coins, Clock } from "lucide-react"
import { ReferralData } from "@/stores/referral-store"

interface ReferralStatsOverviewProps {
  referralData: ReferralData
}

export default function ReferralStatsOverview({ referralData }: ReferralStatsOverviewProps) {
  const totalReferrals = referralData.stats.total_signups || 0
  const totalEarned = referralData.stats.total_earnings || 0

  return (
    <div className="relative overflow-hidden rounded-3xl bg-gradient-to-br from-[rgb(20,136,204)]/10 via-[rgb(32,141,209)]/8 to-[rgb(43,50,178)]/10 border border-[rgb(20,136,204)]/20">
      <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent" />
      <div className="relative p-8 md:p-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
          {/* Left side - Hero content */}
          <div className="space-y-6">
            <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-gradient-to-r from-[rgb(20,136,204)]/20 to-[rgb(43,50,178)]/20 border border-[rgb(20,136,204)]/30">
              <Users className="w-4 h-4 text-[rgb(20,136,204)]" />
              <span className="text-sm font-medium text-foreground">Invite & Earn</span>
            </div>
            <div>
              <h1 className="text-3xl md:text-4xl font-black brand-heading mb-4">Referral Program</h1>
              <p className="text-lg text-muted-foreground leading-relaxed">
                Share your referral code with friends and earn coins together. 
                The more you refer, the higher your tier and bonus rewards!
              </p>
            </div>
            <div className="flex flex-wrap gap-4">
              <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                <TrendingUp className="w-5 h-5 text-blue-400" />
                <span className="text-sm font-medium">15 Coins per Click</span>
              </div>
              <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                <Coins className="w-5 h-5 text-emerald-400" />
                <span className="text-sm font-medium">30 Coins per Signup</span>
              </div>
              <div className="flex items-center gap-2 px-4 py-2 glass-ultra rounded-xl border border-border/50">
                <Users className="w-5 h-5 text-amber-400" />
                <span className="text-sm font-medium">Tier Bonuses</span>
              </div>
            </div>
          </div>

          {/* Right side - Stats grid */}
          <div className="grid grid-cols-2 gap-4">
            <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br from-blue-500 to-cyan-600 flex items-center justify-center">
                <TrendingUp className="w-6 h-6 text-white" />
              </div>
              <p className="text-2xl font-black text-foreground">{referralData.stats.total_clicks || 0}</p>
              <p className="text-sm text-muted-foreground">Total Clicks</p>
            </div>
            <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center">
                <Users className="w-6 h-6 text-white" />
              </div>
              <p className="text-2xl font-black text-foreground">{totalReferrals}</p>
              <p className="text-sm text-muted-foreground">Total Signups</p>
            </div>
            <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br from-emerald-500 to-green-600 flex items-center justify-center">
                <Coins className="w-6 h-6 text-white" />
              </div>
              <p className="text-2xl font-black text-foreground">{totalEarned}</p>
              <p className="text-sm text-muted-foreground">Total Earned</p>
            </div>
            <div className="glass-ultra rounded-2xl p-6 border border-border/50 text-center">
              <div className="w-12 h-12 mx-auto mb-3 rounded-xl bg-gradient-to-br from-yellow-500 to-orange-600 flex items-center justify-center">
                <Clock className="w-6 h-6 text-white" />
              </div>
              <p className="text-2xl font-black text-foreground">{referralData.stats.pending_earnings || 0}</p>
              <p className="text-sm text-muted-foreground">Pending</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
