/**
 * CythroDash - Transfers Database Schema
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

import { ObjectId } from 'mongodb';

// Transfer status enumeration
export enum TransferStatus {
  PENDING = 'pending',       // Transfer initiated but not yet processed
  COMPLETED = 'completed',   // Transfer successfully completed
  FAILED = 'failed',         // Transfer failed due to error
  CANCELLED = 'cancelled',   // Transfer cancelled by user or system
  REVERSED = 'reversed'      // Transfer reversed due to fraud/dispute
}

// Transfer type enumeration
export enum TransferType {
  USER_TO_USER = 'user_to_user',     // Direct user-to-user transfer
  SYSTEM_REWARD = 'system_reward',   // System-generated reward
  SYSTEM_DEDUCTION = 'system_deduction', // System deduction (penalties, etc.)
  REFUND = 'refund'                  // Refund transfer
}

// Security flags for fraud detection
export interface TransferSecurityFlags {
  is_suspicious: boolean;
  risk_score: number;           // 0-100 risk assessment
  fraud_indicators: string[];   // List of potential fraud indicators
  requires_review: boolean;     // Manual review required
  automated_block: boolean;     // Automatically blocked by system
}

// Device information for security tracking
export interface TransferDeviceInfo {
  user_agent: string;
  ip_address: string;
  browser?: string;
  os?: string;
  device_type?: 'desktop' | 'mobile' | 'tablet';
  screen_resolution?: string;
  timezone?: string;
  session_id?: string;
}

// Transfer metadata
export interface TransferMetadata {
  note?: string;                // Optional transfer note/memo
  reference_id?: string;        // External reference ID
  category?: string;            // Transfer category (gift, payment, etc.)
  tags?: string[];             // Custom tags for organization
}

// Main transfer interface
export interface CythroDashTransfer {
  _id?: ObjectId;
  
  // Core transfer data
  id: number;                           // Auto-increment ID
  sender_user_id: number;              // User sending coins
  receiver_user_id: number;            // User receiving coins
  amount: number;                      // Amount of coins transferred
  
  // Transfer details
  type: TransferType;                  // Type of transfer
  status: TransferStatus;              // Current status
  metadata?: TransferMetadata;         // Additional transfer information
  
  // Security and fraud detection
  security: TransferSecurityFlags;     // Security assessment
  device_info: TransferDeviceInfo;     // Device/session information
  
  // Balances (for audit trail)
  sender_balance_before: number;       // Sender's balance before transfer
  sender_balance_after: number;        // Sender's balance after transfer
  receiver_balance_before: number;     // Receiver's balance before transfer
  receiver_balance_after: number;      // Receiver's balance after transfer
  
  // Processing information
  processed_at?: Date;                 // When transfer was processed
  processed_by?: number;               // Admin user ID if manually processed
  failure_reason?: string;             // Reason for failure if applicable
  
  // Timestamps
  created_at: Date;                    // When transfer was initiated
  updated_at: Date;                    // Last update timestamp
  
  // Audit trail
  created_by_ip: string;               // IP address of initiator
  last_modified_by?: number;           // User ID of last modifier
  version: number;                     // Version for optimistic locking
}

// Transfer validation rules
export interface TransferValidationRules {
  min_amount: number;                  // Minimum transfer amount
  max_amount: number;                  // Maximum transfer amount
  max_daily_amount: number;            // Maximum daily transfer limit
  max_daily_count: number;             // Maximum daily transfer count
  cooldown_minutes: number;            // Cooldown between transfers
  require_confirmation: boolean;       // Require email/SMS confirmation
  blocked_users: number[];             // List of blocked user IDs
}

// Database operations interface
export interface TransferOperations {
  // Core CRUD operations
  createTransfer: (transfer: Omit<CythroDashTransfer, '_id' | 'id' | 'created_at' | 'updated_at' | 'version'>) => Promise<CythroDashTransfer>;
  getTransferById: (id: number) => Promise<CythroDashTransfer | null>;
  updateTransfer: (id: number, updates: Partial<CythroDashTransfer>) => Promise<CythroDashTransfer | null>;
  deleteTransfer: (id: number) => Promise<boolean>;
  
  // Query operations
  getTransfersByUser: (userId: number, limit?: number, offset?: number) => Promise<CythroDashTransfer[]>;
  getTransfersBetweenUsers: (senderId: number, receiverId: number, limit?: number) => Promise<CythroDashTransfer[]>;
  getTransfersByStatus: (status: TransferStatus, limit?: number) => Promise<CythroDashTransfer[]>;
  getTransfersByDateRange: (startDate: Date, endDate: Date, userId?: number) => Promise<CythroDashTransfer[]>;
  
  // Analytics operations
  getUserTransferStats: (userId: number, days?: number) => Promise<{
    total_sent: number;
    total_received: number;
    transfer_count: number;
    daily_sent: number;
    daily_received: number;
    daily_count: number;
  }>;
  
  // Security operations
  getFlaggedTransfers: (limit?: number) => Promise<CythroDashTransfer[]>;
  getTransfersByRiskScore: (minRiskScore: number, limit?: number) => Promise<CythroDashTransfer[]>;
  
  // Validation operations
  validateTransferAmount: (amount: number) => boolean;
  validateDailyLimits: (userId: number, amount: number) => Promise<boolean>;
  checkTransferCooldown: (userId: number) => Promise<boolean>;
  detectSuspiciousActivity: (transfer: Partial<CythroDashTransfer>) => Promise<TransferSecurityFlags>;
}

// Transfer helper functions
export const TransferHelpers = {
  // Validate transfer amount format
  isValidAmount: (amount: number): boolean => {
    return Number.isInteger(amount) && amount > 0 && amount <= 1000000;
  },
  
  // Calculate risk score based on various factors
  calculateRiskScore: (transfer: Partial<CythroDashTransfer>, userHistory: CythroDashTransfer[]): number => {
    let riskScore = 0;
    
    // Large amount risk
    if (transfer.amount && transfer.amount > 10000) riskScore += 20;
    if (transfer.amount && transfer.amount > 50000) riskScore += 30;
    
    // Frequency risk
    const recentTransfers = userHistory.filter(t => 
      t.created_at > new Date(Date.now() - 24 * 60 * 60 * 1000)
    );
    if (recentTransfers.length > 10) riskScore += 25;
    if (recentTransfers.length > 20) riskScore += 40;
    
    // New user risk
    const userAge = userHistory.length;
    if (userAge < 5) riskScore += 15;
    
    return Math.min(riskScore, 100);
  },
  
  // Generate transfer reference ID
  generateReferenceId: (): string => {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substring(2, 8);
    return `TXN${timestamp}${random}`.toUpperCase();
  },
  
  // Format transfer amount for display
  formatAmount: (amount: number): string => {
    return amount.toLocaleString();
  },
  
  // Get transfer status color for UI
  getStatusColor: (status: TransferStatus): string => {
    switch (status) {
      case TransferStatus.COMPLETED: return 'text-green-600';
      case TransferStatus.PENDING: return 'text-yellow-600';
      case TransferStatus.FAILED: return 'text-red-600';
      case TransferStatus.CANCELLED: return 'text-gray-600';
      case TransferStatus.REVERSED: return 'text-orange-600';
      default: return 'text-gray-600';
    }
  },
  
  // Get transfer type display name
  getTypeDisplayName: (type: TransferType): string => {
    switch (type) {
      case TransferType.USER_TO_USER: return 'User Transfer';
      case TransferType.SYSTEM_REWARD: return 'System Reward';
      case TransferType.SYSTEM_DEDUCTION: return 'System Deduction';
      case TransferType.REFUND: return 'Refund';
      default: return 'Unknown';
    }
  }
};

// Export collection name for MongoDB
export const TRANSFERS_COLLECTION = 'cythro_dash_transfers';

// Export indexes for optimal performance
export const TRANSFER_INDEXES = [
  { sender_user_id: 1, created_at: -1 },
  { receiver_user_id: 1, created_at: -1 },
  { status: 1, created_at: -1 },
  { 'security.is_suspicious': 1, 'security.risk_score': -1 },
  { created_at: -1 },
  { id: 1 }
];
