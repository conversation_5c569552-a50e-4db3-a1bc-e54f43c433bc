"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Menu, Search, Coins } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import UserMenu from "@/components/user-menu"
import NotificationsMenu from "@/components/notifications-menu"
import { useAuthStore } from "@/stores/user-store"


type Props = {
  sidebarOpen: boolean
  setSidebarOpen: (open: boolean) => void
}

export default function PterodactylHeader({ sidebarOpen, setSidebarOpen }: Props) {
  const [searchQuery, setSearchQuery] = useState("")
  const { currentUser, isAuthenticated } = useAuthStore()

  // Don't check session in header - let AuthWrapper handle authentication
  // The header should only display user data if it's available
  useEffect(() => {
    console.log('PterodactylHeader mounted:', { isAuthenticated, hasUser: !!currentUser })
  }, [isAuthenticated, currentUser])

  // Get user balance, default to 0 if not available
  const balance = currentUser?.coins || 0

  return (
    <header className="h-16 glass-ultra border-b border-border shadow-glass sticky top-0 z-30">
      <div className="flex items-center justify-between h-full px-4 lg:px-6">
        {/* Left side */}
        <div className="flex items-center gap-4">
          {/* Mobile menu button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setSidebarOpen(!sidebarOpen)}
            className="lg:hidden glass-button p-2"
            aria-label="Toggle sidebar"
          >
            <Menu className="w-5 h-5" />
          </Button>

          {/* Search - Hidden on mobile */}
          <div className="relative hidden md:block">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-muted-foreground" />
            <Input
              placeholder="Search servers, users..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-64 glass-input"
            />
          </div>
        </div>

        {/* Right side */}
        <div className="flex items-center gap-2 md:gap-4">
          {/* Coins Balance */}
          <div className="flex items-center gap-2 px-3 py-1.5 glass-ultra rounded-xl border border-border/50 bg-gradient-to-r from-[rgb(20,136,204)]/10 to-[rgb(43,50,178)]/10">
            <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-[rgb(20,136,204)] to-[rgb(43,50,178)] flex items-center justify-center shadow-lg">
              <Coins className="w-3 h-3 text-white" />
            </div>
            <span className="text-sm font-bold text-foreground">{balance.toLocaleString()}</span>
            <Badge className="bg-gradient-to-r from-[rgb(20,136,204)]/20 to-[rgb(43,50,178)]/20 text-foreground border-0 text-xs px-1.5 py-0.5">
              coins
            </Badge>
          </div>

          {/* Notifications */}
          <NotificationsMenu />

          {/* User menu */}
          <UserMenu />
        </div>
      </div>
    </header>
  )
}
