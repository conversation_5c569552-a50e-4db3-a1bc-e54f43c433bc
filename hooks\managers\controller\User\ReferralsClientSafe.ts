/**
 * CythroDash - Client-Safe Referrals Controller Wrapper
 *
 * This wrapper ensures that MongoDB-dependent operations only run on the server side,
 * preventing client-side import issues while maintaining the direct store-to-controller pattern.
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

// Import types only to avoid runtime imports
import type { 
  ReferralClickRequest,
  ReferralSignupRequest,
  ClaimReferralEarningsRequest,
  GetReferralStatsRequest,
  ReferralResponse,
  ReferralStatsResponse
} from './Referrals';

/**
 * Client-safe wrapper for ReferralsController
 * Only loads the actual controller when running on server side
 */
export class ReferralsControllerClientSafe {
  
  private static async getController() {
    // Only import the actual controller on server side
    if (typeof window === 'undefined') {
      const { ReferralsController } = await import('./Referrals');
      return ReferralsController;
    }
    throw new Error('ReferralsController can only be used on server side');
  }

  /**
   * Handle referral click (visiting page via referral link)
   */
  static async handleReferralClick(request: ReferralClickRequest): Promise<ReferralResponse> {
    try {
      const controller = await this.getController();
      return await controller.handleReferralClick(request);
    } catch (error) {
      console.error('Client-side referral click error:', error);
      return {
        success: false,
        message: 'Referral tracking is only available on server side'
      };
    }
  }

  /**
   * Handle referral signup (user registered via referral)
   */
  static async handleReferralSignup(request: ReferralSignupRequest): Promise<ReferralResponse> {
    try {
      const controller = await this.getController();
      return await controller.handleReferralSignup(request);
    } catch (error) {
      console.error('Client-side referral signup error:', error);
      return {
        success: false,
        message: 'Referral tracking is only available on server side'
      };
    }
  }

  /**
   * Claim referral earnings
   */
  static async claimReferralEarnings(request: ClaimReferralEarningsRequest): Promise<ReferralResponse> {
    try {
      const controller = await this.getController();
      return await controller.claimReferralEarnings(request);
    } catch (error) {
      console.error('Client-side claim earnings error:', error);
      return {
        success: false,
        message: 'Claiming earnings is only available on server side'
      };
    }
  }

  /**
   * Get referral statistics and recent activity
   */
  static async getReferralStats(request: GetReferralStatsRequest): Promise<ReferralStatsResponse> {
    try {
      const controller = await this.getController();
      return await controller.getReferralStats(request);
    } catch (error) {
      console.error('Client-side get stats error:', error);
      return {
        success: false,
        message: 'Referral stats are only available on server side'
      };
    }
  }

  /**
   * Generate or regenerate referral code for user
   */
  static async generateReferralCode(userId: number): Promise<ReferralResponse> {
    try {
      const controller = await this.getController();
      return await controller.generateReferralCode(userId);
    } catch (error) {
      console.error('Client-side generate code error:', error);
      return {
        success: false,
        message: 'Referral code generation is only available on server side'
      };
    }
  }
}
