/**
 * CythroDash - Client-Safe Referrals Controller Wrapper
 *
 * This wrapper ensures that MongoDB-dependent operations only run on the server side,
 * preventing client-side import issues while maintaining the direct store-to-controller pattern.
 *
 * When called from client-side, it returns mock/placeholder data to prevent errors
 * while maintaining the same interface.
 *
 * DISCLAIMER: This code is provided as-is for CythroDash.
 * Any modifications or issues arising from the use of this code
 * are not the responsibility of the original developers.
 */

// Import types only to avoid runtime imports
import type {
  ReferralClickRequest,
  ReferralSignupRequest,
  ClaimReferralEarningsRequest,
  GetReferralStatsRequest,
  ReferralResponse,
  ReferralStatsResponse
} from './Referrals';

/**
 * Client-safe wrapper for ReferralsController
 * Loads the actual controller on server side, returns mock data on client side
 */
export class ReferralsControllerClientSafe {

  private static async getController() {
    // Only import the actual controller on server side
    if (typeof window === 'undefined') {
      const { ReferralsController } = await import('./Referrals');
      return ReferralsController;
    }
    return null; // Return null for client-side calls
  }

  private static isClientSide(): boolean {
    return typeof window !== 'undefined';
  }

  /**
   * Handle referral click (visiting page via referral link)
   */
  static async handleReferralClick(request: ReferralClickRequest): Promise<ReferralResponse> {
    if (this.isClientSide()) {
      console.warn('⚠️ Referral click tracking called from client-side, returning mock response');
      return {
        success: true,
        message: 'Referral click tracked (client-side mock)'
      };
    }

    try {
      const controller = await this.getController();
      if (controller) {
        return await controller.handleReferralClick(request);
      }
      return {
        success: false,
        message: 'Controller not available'
      };
    } catch (error) {
      console.error('Server-side referral click error:', error);
      return {
        success: false,
        message: 'Error tracking referral click'
      };
    }
  }

  /**
   * Handle referral signup (user registered via referral)
   */
  static async handleReferralSignup(request: ReferralSignupRequest): Promise<ReferralResponse> {
    if (this.isClientSide()) {
      console.warn('⚠️ Referral signup tracking called from client-side, returning mock response');
      return {
        success: true,
        message: 'Referral signup tracked (client-side mock)'
      };
    }

    try {
      const controller = await this.getController();
      if (controller) {
        return await controller.handleReferralSignup(request);
      }
      return {
        success: false,
        message: 'Controller not available'
      };
    } catch (error) {
      console.error('Server-side referral signup error:', error);
      return {
        success: false,
        message: 'Error tracking referral signup'
      };
    }
  }

  /**
   * Claim referral earnings
   */
  static async claimReferralEarnings(request: ClaimReferralEarningsRequest): Promise<ReferralResponse> {
    if (this.isClientSide()) {
      console.warn('⚠️ Claim earnings called from client-side, returning mock response');
      return {
        success: false,
        message: 'Claiming earnings requires server-side processing'
      };
    }

    try {
      const controller = await this.getController();
      if (controller) {
        return await controller.claimReferralEarnings(request);
      }
      return {
        success: false,
        message: 'Controller not available'
      };
    } catch (error) {
      console.error('Server-side claim earnings error:', error);
      return {
        success: false,
        message: 'Error claiming earnings'
      };
    }
  }

  /**
   * Get referral statistics and recent activity
   */
  static async getReferralStats(request: GetReferralStatsRequest): Promise<ReferralStatsResponse> {
    if (this.isClientSide()) {
      console.warn('⚠️ Get referral stats called from client-side, returning mock data');
      return {
        success: true,
        message: 'Mock referral stats (client-side)',
        data: {
          stats: {
            total_clicks: 0,
            total_signups: 0,
            total_earnings: 0,
            pending_earnings: 0,
            claimed_earnings: 0,
            conversion_rate: 0,
            average_earnings_per_referral: 0,
            successful_referrals: 0,
            base_earnings: 0,
            tier_bonus: 0,
            current_tier: { name: 'Bronze', level: 1, multiplier: 1, min_referrals: 0 },
            tier_progress: { current: 0, next: 5, progress: 0 }
          },
          referral_code: 'LOADING',
          recent_activities: []
        }
      };
    }

    try {
      const controller = await this.getController();
      if (controller) {
        return await controller.getReferralStats(request);
      }
      return {
        success: false,
        message: 'Controller not available'
      };
    } catch (error) {
      console.error('Server-side get stats error:', error);
      return {
        success: false,
        message: 'Error fetching referral stats'
      };
    }
  }

  /**
   * Generate or regenerate referral code for user
   */
  static async generateReferralCode(userId: number): Promise<ReferralResponse> {
    if (this.isClientSide()) {
      console.warn('⚠️ Generate referral code called from client-side, returning mock response');
      return {
        success: false,
        message: 'Referral code generation requires server-side processing'
      };
    }

    try {
      const controller = await this.getController();
      if (controller) {
        return await controller.generateReferralCode(userId);
      }
      return {
        success: false,
        message: 'Controller not available'
      };
    } catch (error) {
      console.error('Server-side generate code error:', error);
      return {
        success: false,
        message: 'Error generating referral code'
      };
    }
  }
}
